from dotenv import load_dotenv
load_dotenv()
from os import getenv

db_url = "postgresql://{}:{}@{}/{}".format(
    getenv('PSOTGERS_USERNAME'),
    getenv('PSOTGERS_PASSWORD'),
    getenv('PSOTGERS_HOST'),
    getenv('PSOTGERS_DBNAME')
)
async_database_url = "postgresql+asyncpg://{}:{}@{}/{}".format(
    getenv('PSOTGERS_USERNAME'),
    getenv('PSOTGERS_PASSWORD'),
    getenv('PSOTGERS_HOST'),
    getenv('PSOTGERS_DBNAME')
) 
env = getenv("ENV", "prod")
model_factory_selected = getenv("MODEL_FACTORY_SELECTED", "flux")
migration_folder = "common/database/migrations"