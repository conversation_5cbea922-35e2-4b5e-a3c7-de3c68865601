from fastapi import HTTPException
from common.models import model_factory
from common.utils.image_handler import <PERSON>Hand<PERSON>
from common.core.service import CoreService
from .schema import ImageRequest, ImageResponse
from config import model_factory_selected
from .prompts import openai_light_correction_prompt, flux_light_correction_prompt


class LightCorrection(CoreService):
    request_model = ImageRequest
    def __init__(self, app) -> None:
        super().__init__(
            client=model_factory.get_model(model_factory_selected),
            app=app,
            url_prefix="/image-enhancement-light-correction/predict",
        )

    def define_prompt(self, level_text: str) -> str:
        match model_factory_selected:
            case "openai":
                return openai_light_correction_prompt.format(level_text=level_text)
            case "flux":
                return flux_light_correction_prompt.format(level_text=level_text)
            case _:
                raise HTTPException(status_code=400, detail="Model not supported")

    async def flow(self, request: ImageRequest) -> ImageResponse | HTTPException:
        instance = request.instances[0]
        image_data = instance.image_url
        enhancement_level = instance.enhancement_level
        apply_low_light = (
            instance.apply_low_light if hasattr(instance, "apply_low_light") else True
        )
        input_image = image_data
        level_text = (
            "slightly"
            if enhancement_level < 0.5
            else "significantly" if enhancement_level > 0.8 else "moderately"
        )
        prompt = self.define_prompt(level_text)
        
        response, token_usage = await self.client.image_edit(
            image=input_image, prompt=prompt
        )
        if isinstance(response, HTTPException):
            return response
        image_base64 = response
        return (
            ImageResponse(
                computer_vision={
                    "result": image_base64,
                    "service": "image-enhancement-light-correction",
                }
            ),
            token_usage,
        )
    async def main(self, request: ImageRequest):
        parsed_request = self.request_model.model_validate(request)
        return await self._main(parsed_request)