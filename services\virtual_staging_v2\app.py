from fastapi import FastAP<PERSON>
from fastapi.middleware.cors import CORSMiddleware
from config import env
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

from services.virtual_staging_v2.flow import VirtualStagingV2
grass_repair = VirtualStagingV2(app)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, timeout_keep_alive=300)