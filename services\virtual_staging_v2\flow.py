from typing import Tu<PERSON>
from fastapi import HTTPException
from common.core.schema import TokenUsage
from common.models import model_factory
from common.utils.image_handler import ImageHandler
from common.utils.logger import logger
from common.core.service import CoreService
from .schema import VirtualStagingRequest, VirtualStagingResponse, VirtualStagingPreProcessing
from config import model_factory_selected


class VirtualStagingV2(CoreService):
    outdoor_prompt = """TASK: Create a photorealistic virtual staging of this outdoor space using high-quality, cohesive design elements suitable for the existing environment.

INSTRUCTIONS:
1. ANALYZE the existing space: lighting, proportions, perspective, and architectural features
2. ADD ONLY the following elements in a tasteful and consistent design:
   • Weather-appropriate outdoor furniture (seating, tables, loungers)
   • Landscape elements (plants, planters, garden features)
   • Outdoor lighting fixtures (string lights, lanterns, sconces)
   • Decorative accessories (cushions, umbrellas, outdoor art)
   • Textiles suitable for outdoor use

TECHNICAL REQUIREMENTS:
- Match existing lighting direction, intensity, and color temperature
- Ensure all furniture scales are proportionally accurate to the space
- Render realistic shadows and reflections for all added elements
- Maintain consistent perspective and depth of field

STRICT CONSTRAINTS:
- NEVER alter: walls, structures, architectural details, flooring, or spatial dimensions
- NEVER change: camera angle, viewpoint, or existing lighting conditions
- NEVER modify: any permanent fixtures or built-in elements

OUTPUT: A seamlessly integrated, photorealistic outdoor staging that appears naturally part of the original photograph."""

    indoor_prompt = """TASK: Create a photorealistic virtual staging of this {room_type} while PRESERVING the original room’s architecture and layout. Apply refined interior design principles to furnish and decorate the space in a cohesive and elegant way.

IMPORTANT: Do NOT modify any structural or architectural elements of the original image.

STRUCTURAL CONSTRAINTS:
- DO NOT ALTER: walls, windows, doors, flooring, ceilings, or fixed architectural features
- RETAIN EXACTLY: room dimensions, camera angle, perspective, and lighting conditions
- PRESERVE: all spatial relationships and depth cues from the original photo

DESIGN OBJECTIVES:
1. ANALYZE the space: note natural light, layout, and focal points
2. APPLY design through:
   - Furniture: scale and placement appropriate to {room_type}
   - Materials: authentic finishes (e.g., textures, woods, fabrics)
   - Lighting: fixtures suited to both natural and artificial light
   - Decor: tasteful, minimalist accessories (avoid clutter)

STYLING GUIDELINES:
- Furniture Layout: respect logical room flow and usability
- Material Authenticity: use quality, style-consistent finishes
- Color Palette: complement existing tones in the space
- Composition Ratio: 70% functional furniture, 30% stylistic decor

RENDERING RULES:
- MATCH: shadows, reflections, and color temperature with the original
- INTEGRATE: furniture naturally into the scene (cast correct shadows, maintain depth of field)
- AVOID: unrealistic proportions or lighting conflicts

OUTPUT: A high-quality, photorealistic virtual staging that highlights the room’s potential without altering its structure."""

    request_model = VirtualStagingRequest
    def __init__(self, app) -> None:
        super().__init__(
            client=model_factory.get_model(model_factory_selected),
            app=app,
            url_prefix="/v2/virtual-staging-refurnishing-v2/predict",
        )
    async def flow(self, request: VirtualStagingRequest) -> VirtualStagingResponse | HTTPException:
        
        input_image = request.image
        if request.scene_type == "outdoor":
            prompt = self.outdoor_prompt
        else:
            if not request.room_type:
                return HTTPException(status_code=400, detail="Room type is required for indoor scenes"), None
            prompt = self.indoor_prompt.format(room_type=request.room_type)
            

        response, token_usage = await self.client.image_edit(
            image=input_image, prompt=prompt
        )
        if isinstance(response, HTTPException):
            return response
        image_base64 = response
        return (
            VirtualStagingResponse(image=image_base64),
            [token_usage ],
        )
    async def main(self, request: VirtualStagingRequest):
        parsed_request = self.request_model.model_validate(request)
        return await self._main(parsed_request)