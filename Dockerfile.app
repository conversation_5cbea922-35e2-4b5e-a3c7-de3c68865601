FROM python:3.12-slim-bookworm AS builder

RUN apt-get update && apt-get install -y \
    curl \
    && rm -rf /var/lib/apt/lists/*

RUN curl -LsSf https://astral.sh/uv/install.sh | sh
ENV PATH="/root/.local/bin:${PATH}"

WORKDIR /app

COPY common/requirements.txt .

RUN uv pip install --target=/app/.deps -r requirements.txt

FROM python:3.12-slim-bookworm
ENV LD_LIBRARY_PATH=/usr/lib/x86_64-linux-gnu:$LD_LIBRARY_PATH
ENV PYTHONPATH=/app/.deps:$PYTHONPATH

WORKDIR /app
COPY --from=builder /app/.deps /app/.deps
COPY common/ common/
COPY services/ services/
COPY app.py config.py ./

EXPOSE 8000
CMD ["python", "app.py"]