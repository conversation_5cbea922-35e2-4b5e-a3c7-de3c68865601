from yoyo import step

__depends__ = {"20250508_01_init_db"}

steps = [
    step(
        """
        CREATE TABLE IF NOT EXISTS request (
            id SERIAL PRIMARY KEY,
            request JSON NOT NULL,
            endpoint_call VARCHAR NOT NULL,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        );
        """,
        """
        DROP TABLE IF EXISTS request;
        """,
    ),
    step(
        """
        CREATE TABLE IF NOT EXISTS response (
            id SERIAL PRIMARY KEY,
            request_id INTEGER NOT NULL,
            status INTEGER NOT NULL,
            error_message VARCHAR,
            response JSON NOT NULL,
            created_at TIMESTAMPTZ DEFAULT CURRENT_TIMESTAMP
        );
        """,
        """
        DROP TABLE IF EXISTS response;
        """,
    ),
    step(
        """
        ALTER TABLE response
        ADD CONSTRAINT fk_response_request
        FOREIGN KEY (request_id) REFERENCES request(id);
        """,
        """
        ALTER TABLE response
        DROP CONSTRAINT fk_response_request;
        """,
    ),
    step(
        """
        CREATE TABLE IF NOT EXISTS openai_token (
            id SERIAL PRIMARY KEY,
            request_id INTEGER NOT NULL,
            input_token INTEGER,
            image_token INTEGER,
            output_token INTEGER   
        );
        """,
        """
        DROP TABLE IF EXISTS openai_token;
        """,
    ),
    step(
        """
        ALTER TABLE openai_token
        ADD CONSTRAINT fk_openai_token_request
        FOREIGN KEY (request_id) REFERENCES request(id);
        """,
        """
        ALTER TABLE openai_token
        DROP CONSTRAINT fk_openai_token_request;
        """,
    ),
]

