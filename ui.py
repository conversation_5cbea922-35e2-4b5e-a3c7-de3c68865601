from fastapi.routing import APIRouter
from fastapi.responses import HTMLResponse
from fastapi import Request
import json
router = APIRouter()

@router.get("/", response_class=HTMLResponse)
async def debug_ui(request: Request):

    # Get OpenAPI spec and predict endpoints
    spec = request.app.openapi()

    html_template = open("template.html").read()
    return HTMLResponse(html_template.replace("{{SPEC_JSON}}", json.dumps(spec)))
