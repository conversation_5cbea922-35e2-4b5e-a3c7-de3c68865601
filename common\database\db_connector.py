from os import getenv
from sqlalchemy.orm import sessionmaker
from sqlalchemy.ext.asyncio import AsyncSession, create_async_engine
from sqlalchemy.pool import NullPool
from config import async_database_url


class Connection:
    def __init__(self) -> None:
        
        self.engine = create_async_engine(
            async_database_url,
            connect_args={"timeout": 5},
            poolclass=NullPool,
            echo=True if getenv("SHOW_SQL").lower() == "true" else False,
        )

    def get_session(self) -> AsyncSession:
        self.SessionLocal = sessionmaker(bind=self.engine, class_=AsyncSession)
        return self.SessionLocal()


connector: Connection = Connection()
