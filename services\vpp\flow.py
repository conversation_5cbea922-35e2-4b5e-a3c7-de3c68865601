from fastapi import HTTPException
from common.database.repositories.retailers import RetailerRepository
from common.models import model_factory
from common.utils.image_handler import <PERSON>Handler
from common.core.service import CoreService
from config import model_factory_selected
from common.core.schema import TokenUsage
from typing import List, Tuple
from common.utils.logger import logger
from common.database.models.retailers import Item
from .schema import (
    PostProcessing,
    PreProcessing,
    GenereativeMessage,
    ImageGenerationResponse,
    ImageRequest,
)


class Vpp(CoreService):
    request_model = ImageRequest

    def __init__(self, app) -> None:
        super().__init__(
            client=model_factory.get_model(model_factory_selected),
            app=app,
            url_prefix="/virtual-staging-product-placement/predict",
        )
        self.retailer = RetailerRepository()

    async def preprocessing_image(self, image: str) -> Tuple[PreProcessing, TokenUsage]:
        try:
            from common.database.models.retailers import RoomType

            logger.debug("Fetching room types from database")
            room_types = await self.retailer.get_all(RoomType)
            room_types = [rt.name for rt in room_types]
            logger.debug(f"Found {len(room_types)} room types")
        except Exception as e:
            logger.error(f"Error getting room types: {e}", exc_info=True)
            return (
                HTTPException(status_code=500, detail="Failed to get room types"),
                None,
            )
        prompt = f"Check the image and detect only one room type from the following list : {room_types}  the `room_type` should not be empty and should be slelected.  If the provided image is not a room , return `is_relevant` as False , otherwise as `True` . \n JSNON output should have two keys: `room_type` and  `is_relevant` "

        messages = [
            {
                "role": "system",
                "content": "You are a helpful assistant that analyse images for real estate.",
            },
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/png;base64,{image}"},
                    },
                ],
            },
        ]
        room_type, tokens = await self.client.chat_completion(
            messages=messages, structured_output=PreProcessing
        )
        logger.info(
            f"Preprocessing complete. Room type: {room_type.room_type}, Relevant: {room_type.is_relevant}"
        )
        return room_type, tokens

    async def generative_prompt(self, image: str, items: list[Item]):
        logger.debug(f"Working with {len(items)} items")
        prompt = f"Given the image, generate a prompt to refurnish an image of the room or stage it with the items provided. The items are {items}"
        messages = [
            {
                "role": "system",
                "content": (
                    "You are a helpful assistant that analyses images for real estate. "
                    "Follow these strict constraints when interpreting or generating image content: "
                    "Don't modify the wall structure, color, or texture. "
                    "Don't alter the fireplace design or placement. "
                    "Don't change the camera angle or image perspective. "
                    "Don't adjust the ceiling details or lighting fixtures. "
                    "Don't modify the floor material, color, or layout. "
                    "Don't alter the architectural structure of the room. "
                    "Don't change the size, shape, or positioning of the windows. "
                    "Don't modify the windowpanes or their framing elements."
                ),
            },
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/png;base64,{image}"},
                    },
                ],
            },
        ]
        generative_message, tokens = await self.client.chat_completion(
            messages=messages, structured_output=GenereativeMessage
        )
        return generative_message, tokens

    async def image_generation(
        self, image: str, generative_message: GenereativeMessage
    ) -> Tuple[ImageGenerationResponse, TokenUsage]:
        input_image = ImageHandler.base64_to_bytes(image)
        image_base64, tokens = await self.client.image_edit(
            image=input_image, prompt=generative_message.prompt
        )
        if isinstance(image_base64, HTTPException):
            return image_base64, None
        return (
            ImageGenerationResponse(image=image_base64, items=generative_message.items),
            tokens,
        )

    async def postprocess_image(self, image, items: list[Item]):
        prompt = f"Given the image, check if the following are existant in the image. The items are {items}"
        messages = [
            {
                "role": "system",
                "content": "You are a helpful assistant that analyse images for real estate.",
            },
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": prompt},
                    {
                        "type": "image_url",
                        "image_url": {"url": f"data:image/png;base64,{image}"},
                    },
                ],
            },
        ]
        post_processing, tokens = await self.client.chat_completion(
            messages=messages, structured_output=PostProcessing
        )
        return post_processing, tokens

    async def flow(
        self, request: ImageRequest
    ) -> Tuple[ImageGenerationResponse, List[TokenUsage]] | HTTPException:
        # usages =[]
        image = request.image

        preprocessing_response, preprocessing_usage = await self.preprocessing_image(
            image=image
        )
        if isinstance(preprocessing_response, HTTPException):
            return preprocessing_response
        try:
            logger.debug(
                f"Fetching items for room type: {preprocessing_response.room_type}"
            )
            items = await self.retailer.get_random_items_by_room_type(
                preprocessing_response.room_type
            )
            # Convert SQLAlchemy Item objects to Pydantic Item objects
            from .schema import Item as PydanticItem

            processed_items = []
            for category, item in items:
                pydantic_item = PydanticItem(
                    id=item.id,
                    name=item.name,
                    description=item.description,
                    category=category,
                )
                processed_items.append(pydantic_item)
            items = processed_items
            logger.debug(f"Retrieved {len(items)} items")
        except Exception as e:
            return HTTPException(status_code=500, detail="Failed to fetch items")
        logger.debug("Starting generative prompt phase")
        generative_response, generative_prompt_usage = await self.generative_prompt(
            image=image, items=items
        )
        if isinstance(generative_response, HTTPException):
            return generative_response
        image_response, image_generation_usage = await self.image_generation(
            image=image, generative_message=generative_response
        )
        if isinstance(image_response, HTTPException):
            return image_response
        post_processing_response, post_processing_usage = await self.postprocess_image(
            image_response.image, items
        )
        if not isinstance(post_processing_response, Exception):
            image_response.items = post_processing_response.items
        tokens = [
            preprocessing_usage,
            generative_prompt_usage,
            image_generation_usage,
            post_processing_usage,
        ]
        return image_response, tokens

    async def main(self, request: ImageRequest):
        parsed_request = self.request_model.model_validate(request)
        return await self._main(parsed_request)
