from rich.logging import <PERSON><PERSON><PERSON><PERSON>
from rich.console import Console
import logging
import sys
import os
from config import env

# Create console for rich output
console = Console()
current_env = env


# Configure logging with Rich handler
def setup_logger(name=None):
    """Set up and return a logger with Rich formatting"""
    logging_level = logging.DEBUG if current_env == "dev" else logging.INFO

    logger = logging.getLogger(name or "virtual-staging")
    logger.setLevel(logging_level)

    # Remove existing handlers if any
    if logger.handlers:
        logger.handlers.clear()

    # Add Rich handler
    rich_handler = RichHandler(
        rich_tracebacks=True, console=console, tracebacks_show_locals=True, markup=True
    )
    rich_handler.setLevel(logging_level)
    logger.addHandler(rich_handler)

    return logger


# Create a default logger instance
logger = setup_logger()
