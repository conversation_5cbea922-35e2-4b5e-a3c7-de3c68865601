from sqlalchemy import select, insert, update, delete, func
from sqlalchemy.ext.asyncio import AsyncSession
from typing import List, Optional, Dict, Any, Tuple
from common.database.models.retailers import RoomType, Category, Item
from common.database.db_connector import connector


class RetailerRepository:
    session: AsyncSession = connector.get_session()

    async def insert(self, model: RoomType | Category | Item):
        async with self.session.begin():
            stmt = insert(model.__table__).values(**model.__dict__)
            await self.session.execute(stmt)
            await self.session.commit()
    
    async def get_all(self, model_class: type[RoomType | Category | Item]) -> List:
        stmt = select(model_class)
        result = await self.session.execute(stmt)
        return result.scalars().all()
    
    async def get_by_id(self, model_class: type[RoomType | Category | Item], id: int):
        stmt = select(model_class).where(model_class.id == id)
        result = await self.session.execute(stmt)
        return result.scalars().first()
    
    async def update(self, model: RoomType | Category | Item):
        async with self.session.begin():
            stmt = update(model.__table__).where(
                model.__table__.c.id == model.id
            ).values(**{k: v for k, v in model.__dict__.items() if not k.startswith('_')})
            await self.session.execute(stmt)
            await self.session.commit()
    
    async def delete(self, model: RoomType | Category | Item):
        async with self.session.begin():
            stmt = delete(model.__table__).where(model.__table__.c.id == model.id)
            await self.session.execute(stmt)
            await self.session.commit()
    
    async def close(self):
        await self.session.close()
        
    async def insert_nested_data(self, data):
        """
        Insert nested data structure into the database.
        
        Expected format:
        {
          "Room Type Name": [
            {
              "Category Name": [
                {
                  "productName": "Item Name",
                  "productDescription": "Description",
                  "productlink": "URL",
                  "imageLink": "Image URL"
                },
                ...
              ]
            },
            ...
          ],
          ...
        }
        """
        try:
            async with self.session.begin():
                # Process each room type
                print(f"Type of data: {type(data)}")
                for room_type_name, categories_list in data.items():
                    # Create or get room type
                    room_type_result = await self.session.execute(
                        select(RoomType).where(RoomType.name == room_type_name)
                    )
                    room_type = room_type_result.scalars().first()
                    
                    if not room_type:
                        room_type = RoomType(name=room_type_name)
                        self.session.add(room_type)
                        await self.session.flush()  # Get ID without committing
                    
                    # Process each category group
                    for category_group in categories_list:
                        for category_name, items_list in category_group.items():
                            # Create or get category
                            category_result = await self.session.execute(
                                select(Category).where(
                                    Category.name == category_name.strip(),
                                    Category.room_type_id == room_type.id
                                )
                            )
                            category = category_result.scalars().first()
                            
                            if not category:
                                category = Category(
                                    name=category_name.strip(),
                                    room_type_id=room_type.id
                                )
                                self.session.add(category)
                                await self.session.flush()  # Get ID without committing
                            
                            # Process each item
                            for item_data in items_list:
                                # Convert Pydantic model to dict if needed
                                if hasattr(item_data, "model_dump"):
                                    item_dict = item_data.model_dump()
                                else:
                                    item_dict = item_data
                                
                                # Check if item already exists
                                item_result = await self.session.execute(
                                    select(Item).where(
                                        Item.name == item_dict["productName"],
                                        Item.category_id == category.id
                                    )
                                )
                                item = item_result.scalars().first()
                                
                                if not item:
                                    # Convert HttpUrl objects to strings
                                    image_url = item_dict.get("imageLink", "")
                                    if hasattr(image_url, "__str__"):
                                        image_url = str(image_url)
                                    
                                    product_link = item_dict.get("productLink", "")
                                    if hasattr(product_link, "__str__"):
                                        product_link = str(product_link)
                                    
                                    # Get description with fallback to empty string
                                    description = item_dict.get("productDescription", "") or ""
                                    
                                    item = Item(
                                        name=item_dict["productName"],
                                        description=description,
                                        item_link=product_link or "",
                                        image_url=image_url or "",
                                        category_id=category.id
                                    )
                                    self.session.add(item)
            
            # Commit all changes at once
            await self.session.commit()
            return True
        except Exception as e:
            await self.session.rollback()
            print(f"Error inserting nested data: {e}")
            return False

    async def get_random_items_by_room_type(self, room_type_name: str) -> List[Tuple[str, Item]]:
        """
        Get one random item per category for a given room type.
        
        Args:
            room_type_name: The name of the room type
            
        Returns:
            List of tuples containing (category_name, item)
        """
        try:
            # First, get the room type
            room_type_result = await self.session.execute(
                select(RoomType).where(RoomType.name == room_type_name)
            )
            room_type = room_type_result.scalars().first()
            
            if not room_type:
                return []
            
            # Get all categories for this room type
            categories_result = await self.session.execute(
                select(Category).where(Category.room_type_id == room_type.id)
            )
            categories = categories_result.scalars().all()
            
            result = []
            
            # For each category, get one random item
            for category in categories:
                # Using func.random() for PostgreSQL to get a random item
                random_item_result = await self.session.execute(
                    select(Item).where(Item.category_id == category.id).order_by(func.random()).limit(1)
                )
                random_item = random_item_result.scalars().first()
                
                if random_item:
                    result.append((category.name, random_item))
            
            return result
        except Exception as e:
            print(f"Error getting random items by room type: {e}")
            return []
