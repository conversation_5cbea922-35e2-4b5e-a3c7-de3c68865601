# OpenAI prompt - detailed and comprehensive
openai_grass_repair_prompt = """Enhance this image while strictly preserving all objects, buildings, and structures:
1. If grass is present, enhance only the grass area while keeping it natural looking
2. If no grass is present, enhance overall image quality while preserving all elements
3. DO NOT modify any buildings, furniture, objects, or structures
4. Keep all architectural elements exactly as they are
5. Preserve all man-made objects and their details
6. Maintain original lighting conditions
7. Do not add or remove any elements
8. If enhancing grass, keep it looking natural and not artificial
9. Preserve all edges and boundaries between different elements
10. Keep all textures and materials unchanged"""

# Flux prompt - improved following Kontext documentation guidelines
flux_grass_repair_prompt = """Replace damaged or brown grass areas with lush green healthy lawn while maintaining the exact same camera angle, position, and framing. Keep all buildings, objects, and structures exactly unchanged. Preserve original lighting conditions and natural texture. Maintain the original image quality and all unmodified elements exactly as they appear."""