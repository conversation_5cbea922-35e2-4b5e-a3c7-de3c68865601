from typing import List, Optional
from pydantic import BaseModel
from common.core.schema import RequestSchema, ResponseSchema

class Instance(BaseModel):
    image_url: str
    enhancement_level: float = 0.7
class ImageRequest(RequestSchema):
    instances: list[Instance]
#class ImageResponse(BaseModel):
#    final_image: str
#    mask: str
#    inpainted_image: str
#    upscaled_image: str 
class ComputerVisionResponse(BaseModel):
    result: str
    service: str
    time_taken: float

class ImageResponse(BaseModel):
    computer_vision: dict
