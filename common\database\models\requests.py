from sqlalchemy import <PERSON>umn, Inte<PERSON>, <PERSON>, <PERSON><PERSON><PERSON>, JSON, DateTime, text
from sqlalchemy.orm import relationship
from sqlalchemy.sql import func
from sqlalchemy.orm import mapped_column, Mapped, relationship
from typing import Optional, Dict, Any
from datetime import datetime
from .___base_model import Base


class Request(Base):
    __tablename__ = "request"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    request: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False)
    endpoint_call: Mapped[str] = mapped_column(String, nullable=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=text("timezone('utc', now())"),
        nullable=False,
    )

    # Relationships
    responses = relationship(
        "Response", back_populates="request", cascade="all, delete-orphan"
    )
    tokens = relationship(
        "OpenAIToken", back_populates="request", cascade="all, delete-orphan"
    )

    def __repr__(self) -> str:
        return f"Request(id={self.id}, endpoint_call={self.endpoint_call}, created_at={self.created_at})"


class Response(Base):
    __tablename__ = "response"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    request_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("request.id"), nullable=False
    )
    status: Mapped[int] = mapped_column(Integer, nullable=False)
    error_message: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    response: Mapped[Dict[str, Any]] = mapped_column(JSON, nullable=False)
    created_at: Mapped[datetime] = mapped_column(
        DateTime(timezone=True),
        server_default=text("timezone('utc', now())"),
        nullable=False,
    )

    # Relationship
    request = relationship("Request", back_populates="responses")

    def __repr__(self) -> str:
        return f"Response(id={self.id}, request_id={self.request_id}, status={self.status})"


class OpenAIToken(Base):
    __tablename__ = "openai_token"

    id: Mapped[int] = mapped_column(Integer, primary_key=True, autoincrement=True)
    request_id: Mapped[int] = mapped_column(
        Integer, ForeignKey("request.id"), nullable=False
    )
    input_token: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    image_token: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    output_token: Mapped[Optional[int]] = mapped_column(Integer, nullable=True)
    model_name: Mapped[Optional[str]] = mapped_column(String, nullable=True)
    quality: Mapped[Optional[str]] = mapped_column(String, nullable=True)

    # Relationship
    request = relationship("Request", back_populates="tokens")

    def __repr__(self) -> str:
        return f"OpenAIToken(id={self.id}, request_id={self.request_id}, input_token={self.input_token}, output_token={self.output_token})"
