import base64
import requests
import os
from concurrent.futures import ThreadPoolExecutor

# Define architectural styles
styles = [
    "coastal",
    "contemporary",
    "countryside",
    "industrial",
    "modern",
    "scandinavian",
    "wooden",
    "traditional",
]

# API endpoint
link = "http://localhost:8000/virtual-staging-refurnishing/predict"

# Path to input image folder
folder_path = "appartements"
listings = os.listdir(folder_path)

# Room type mapper
def map_room_type(path: str):
    match path.lower():
        case _ if "living" in path:
            return "living room"
        case _ if "bedroom" in path:
            return "bedroom"
        case _ if "kitchen" in path:
            return "kitchen"
        case _ if "bathroom" in path:
            return "bathroom"
        case _:
            return "other"

# Image file -> Base64
def image_to_base64(path):
    with open(path, "rb") as image_file:
        return base64.b64encode(image_file.read()).decode("utf-8")

# Base64 -> image bytes
def base64_to_image(base64_string):
    return base64.b64decode(base64_string)

# Main image generation function
def generate_image(path):
    def send_request(style):
        room_type = map_room_type(path)
        image = image_to_base64(path)
        payload = {
            "image": image,
            "architecture_style": style,
            "room_type": room_type,
            "scene_type": "indoor",
        }

        try:
            response = requests.post(link, json=payload)
            response.raise_for_status()
            data = response.json().get("data", {})
            image = data.get("image")

            if not image:
                print(f"[WARN] No image returned for {style} - {path}")
                return

            image_name = os.path.basename(path)
            output_path = os.path.join(
                os.path.dirname(path), "ai-generated", style, image_name
            )
            os.makedirs(os.path.dirname(output_path), exist_ok=True)

            with open(output_path, "wb") as f:
                f.write(base64_to_image(image))
            print(f"[OK] Saved: {output_path}")

        except Exception as e:
            print(f"[ERROR] {style} - {path}: {e}")

    with ThreadPoolExecutor(max_workers=len(styles)) as executor:
        executor.map(send_request, styles)
        
        
def main(listing =None):
  # listing_path = os.path.join(folder_path, listing)
  images = ["appartements/listing1/kitchen.jpg",
"appartements/listing1/kitchen.jpg",
"appartements/listing6/kitchen.jpg",
"appartements/listing6/kitchen.jpg",
"appartements/listing6/kitchen.jpg",
"appartements/listing4/bathroom.jpg",
"appartements/listing2/bathroom.jpg",
"appartements/listing1/livingroom.jpg",
"appartements/listing2/bathroom.jpg",]
  for file in images:
      # full_path = os.path.join(listing_path, file)
      full_path = file
      if os.path.isfile(full_path) and file.lower().endswith((".jpg", ".jpeg", ".png")):
          generate_image(full_path)
if __name__ == "__main__":
  # Process all valid images
  # print(listings)
  # with ThreadPoolExecutor(max_workers=len(listings)) as executor:
  #     executor.map(main, listings)
  main()
