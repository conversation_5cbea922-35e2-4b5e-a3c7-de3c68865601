from fastapi import HTTPException
from common.models import model_factory
from common.utils.image_handler import ImageHandler
from common.core.service import CoreService
from .schema import ImageRequest, ImageResponse
from config import model_factory_selected


class Decluttering(CoreService):
    request_model = ImageRequest

    def __init__(self, app) -> None:
        super().__init__(
            client=model_factory.get_model(model_factory_selected),
            app=app,
            url_prefix="/generative-image-decluttering/predict",
        )

    async def flow(self, request: ImageRequest) -> ImageResponse | HTTPException:

        input_image = request.image
        if request.roomType in ["Bedroom", "Living Room"]:
            prompt = "Remove all furniture and decorative items such as sofas, chairs, tables, carpets, lamps, curtains, artwork, plants, and personal belongings. Keep the architectural structure, flooring, walls, windows, and built-in fixtures intact. The room should appear clean, empty, and ready for staging."
        elif request.roomType in ["Kitchen", "Bathroom"]:
            prompt = "Remove small clutter such as food items, dish racks, sponges, cutting boards, dish towels, soap bottles, toothbrushes, toothpaste, shampoo, conditioner, shower gels, razors, cosmetics, laundry baskets, cleaning supplies, and other personal objects. Keep all essential and built-in elements, including cabinets, countertops, sinks, appliances, toilets, bathtubs, showers, and faucets, along with the architectural structure, flooring, walls, windows, and built-in fixtures. Maintain the original colors, retain all existing elements that are not removed, and leave the places of removed items empty so the space appears clean, organized"

        # prompt = """
        # Remove all furniture from this image, including wall decorations, kitchen elements,
        # and any other items, leaving only the bare structure of the space
        # (walls, floor, windows, doors and ceiling).
        # If the image is already an empty room, do not change it.
        # """
        response, token_usage = await self.client.image_edit(
            image=input_image, prompt=prompt
        )
        if isinstance(response, HTTPException):
            return response
        image_base64 = response
        return (
            ImageResponse(image=image_base64),
            token_usage,
        )

    async def main(self, request: ImageRequest):
        parsed_request = self.request_model.model_validate(request)
        return await self._main(parsed_request)
