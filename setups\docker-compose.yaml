version: '3.8'

# services:
#   postgres:
#     image: postgres
#     container_name: postgres
#     environment:
#       POSTGRES_PASSWORD: 123456
#     ports:
#       - "5432:5432"
#     volumes:
#       - ./postgres-data:/var/lib/postgresql/data
#       - ./certs:/certs:rw     
#     restart: always


services:
  postgres:
    image: postgres
    container_name: postgres
    environment:
      POSTGRES_USER: postgres
      POSTGRES_PASSWORD: 123456
      POSTGRES_DB: stagingv2
    ports:
      - "5432:5432"
    volumes:
      - ./postgres-data:/var/lib/postgresql/data
      - ./certs:/certs:rw     
    restart: always

volumes:
  postgres-data:
