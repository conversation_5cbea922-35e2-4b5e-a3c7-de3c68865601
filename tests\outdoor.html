<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Processor with Style Variations</title>
    <style>
        body {
            font-family: sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #111;
            /* Dark background for neon effect */
            color: #fff;
        }

        #container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            color: #cc66ff;
            /* Purple */
            text-align: center;
        }

        button {
            background-color: #33ccff;
            /* Neon Blue */
            color: #000;
            padding: 10px 20px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            border-radius: 5px;
        }

        button:hover {
            background-color: #66ffcc;
            /* A different neon color */
        }

        table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #cc66ff;
            /* Purple border */
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            color: #eee;
            /* Light text for contrast */
        }

        th {
            background-color: #333;
            color: #33ccff;
            /* Neon Blue */
        }

        img {
            max-width: 150px;
            max-height: 150px;
            cursor: pointer;
            /* Indicate it's clickable */
        }

        .fullscreen-image {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
            /* Maintain aspect ratio */
            background-color: rgba(0, 0, 0, 0.9);
            /* Darken background */
            z-index: 1000;
        }

        #logs {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #cc66ff;
            /* Purple border */
            height: 150px;
            overflow-y: scroll;
            color: #eee;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
</head>

<body>

    <div id="container">
        <h1>Image Style Processor</h1>
        <button id="submitButton">Process Images</button>
        <button id="downloadAll">Download All Images</button>
        <table id="imageTable">
            <thead>
                <tr>
                    <th>Original Image</th>
                    <th>Coastal</th>
                    <th>Scandinavian</th>
                    <th>Italian</th>
                    <th>Industrial</th>
                </tr>
            </thead>
            <tbody>
                <!-- Image rows will be inserted here -->
            </tbody>
        </table>

        <h2>Logs</h2>
        <div id="logs"></div>
    </div>

    <script>
        const imageTable = document.getElementById('imageTable').getElementsByTagName('tbody')[0];
        const submitButton = document.getElementById('submitButton');
        const downloadButton = document.getElementById('downloadAll');
        const logs = document.getElementById('logs');

        // **IMPORTANT:** Replace with your actual list of images
        const images = ['garden0.jpg',
            'garden1.jpg',
            'garden2.jpg',
            'yard0.jpg',
            'yard1.jpg',];

        const architectureStyles = ["coastal", "scandinavian", "italian", "industrial"];

        // Function to show the image in fullscreen
        function showFullscreen(imgSrc) {
            const fullscreenDiv = document.createElement('div');
            fullscreenDiv.classList.add('fullscreen-image');
            fullscreenDiv.style.backgroundImage = `url(${imgSrc})`;
            fullscreenDiv.style.backgroundRepeat = 'no-repeat';
            fullscreenDiv.style.backgroundPosition = 'center';
            document.body.appendChild(fullscreenDiv);

            document.addEventListener('keydown', function (e) {
                if (e.key === 'Escape' || e.key === 'Esc') {  // Escape key
                    document.body.removeChild(fullscreenDiv);
                }
            });

        }

        function attachImageEvents() {
            const images = document.querySelectorAll('#imageTable img');

            images.forEach(img => {
                img.addEventListener('click', function () {
                    showFullscreen(this.src);
                });
            });
        }

        // Populate the table with image rows AND attach event listeners to initial images
        images.forEach(imageName => {
            const row = imageTable.insertRow();

            // Original Image Cell
            let cell = row.insertCell();
            cell.innerHTML = `<img src="outdoor/${imageName}" alt="${imageName}">`;

            // Style Placeholder Cells
            architectureStyles.forEach(() => {
                cell = row.insertCell();
                cell.innerHTML = "Processing..."; // Initial placeholder
                cell.classList.add("result-cell"); // Add a class to result cells
            });
        });

        // Attach event listeners to initial images
        attachImageEvents();

        submitButton.addEventListener('click', async () => {
            submitButton.disabled = true;
            logMessage("Starting image processing...");

            const allPromises = [];

            for (let i = 0; i < images.length; i++) {
                const imageName = images[i];
                const row = imageTable.rows[i];

                for (let j = 0; j < architectureStyles.length; j++) {
                    const architectureStyle = architectureStyles[j];
                    const resultCell = row.cells[j + 1];

                    const promise = (async () => {
                        try {
                            const base64Image = await imageToBase64(`outdoor/${imageName}`);
                            const apiResponse = await sendToAPI(imageName, base64Image, architectureStyle);

                            if (apiResponse && apiResponse.data.image) {
                                resultCell.innerHTML = `<img src="data:image/jpeg;base64,${apiResponse.data.image}" alt="${architectureStyle}">`;
                                logMessage(`Processed ${imageName} with ${architectureStyle}: Success`);
                            } else {
                                resultCell.innerHTML = "Error: Invalid API Response";
                                logMessage(`Error processing ${imageName} with ${architectureStyle}: Invalid API Response`);
                            }

                        } catch (error) {
                            resultCell.innerHTML = "Error";
                            logMessage(`Error processing ${imageName} with ${architectureStyle}: ${error}`);
                        }
                    })();

                    allPromises.push(promise);
                }
            }

            await Promise.all(allPromises);
            //ReAttach event listeners to new images as the Dom changes
            attachImageEvents();
            submitButton.disabled = false;
            logMessage("Image processing complete.");

        });
        downloadButton.addEventListener('click', async () => {
            const zip = new JSZip();

            for (let i = 0; i < images.length; i++) {
                const row = imageTable.rows[i];
                const originalImg = row.cells[0].querySelector('img');
                const originalName = originalImg.alt;
                const baseName = originalName.substring(0, originalName.lastIndexOf('.')).toLowerCase();

                // Determine room type
                let roomType = baseName;
                if (baseName.includes('garden')) {
                    roomType = 'garden';
                } else if (baseName.includes('yard')) {
                    roomType = 'yard';
                }

                // Add original image
                const originalBase64 = await imageToBase64(originalImg.src);
                zip.file(`generated_images/${roomType}/${originalName}`, originalBase64, { base64: true });

                // Add generated versions
                for (let j = 0; j < architectureStyles.length; j++) {
                    const style = architectureStyles[j];
                    const generatedImg = row.cells[j + 1].querySelector('img');
                    if (generatedImg) {
                        const generatedBase64 = generatedImg.src.split(',')[1];
                        const generatedFileName = `${baseName}_${style}.jpg`;
                        zip.file(`generated_images/${roomType}/${generatedFileName}`, generatedBase64, { base64: true });
                    }
                }
            }

            zip.generateAsync({ type: "blob" }).then(content => {
                saveAs(content, "generated_images.zip");
            });
        });

        // Helper functions

        async function imageToBase64(imagePath) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.crossOrigin = 'anonymous';
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    canvas.width = img.width;
                    canvas.height = img.height;

                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0);

                    const dataURL = canvas.toDataURL('image/jpeg');
                    resolve(dataURL.substring(dataURL.indexOf(',') + 1));
                };
                img.onerror = (error) => {
                    reject(error);
                };
                img.src = imagePath;
            });
        }

        async function sendToAPI(imageName, base64Image, architectureStyle) {
            const apiURL = 'http://localhost:8000/virtual-staging-refurnishing/predict';

            const baseName = imageName.substring(0, imageName.lastIndexOf('.'));
            let roomType = baseName;
            if (baseName.includes('garden')) {
                roomType = 'garden';
            } else if (baseName.includes('yard')) {
                roomType = 'yard';
            }
            const payload = {
                image: base64Image,
                architecture_style: architectureStyle,
                room_type: roomType,
                scene_type: "outdoor"
            };

            const response = await fetch(apiURL, {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`API request failed with status ${response.status}`);
            }

            return await response.json();
        }

        function logMessage(message) {
            const logEntry = document.createElement('p');
            logEntry.textContent = message;
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
        }

    </script>

</body>

</html>