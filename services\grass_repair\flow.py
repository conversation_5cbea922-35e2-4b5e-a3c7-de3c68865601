from fastapi import HTTPException
from common.models import model_factory
from common.utils.image_handler import <PERSON>Hand<PERSON>
from common.core.service import CoreService
from .schema import ImageRequest, ImageResponse
from config import model_factory_selected
from .prompts import openai_grass_repair_prompt, flux_grass_repair_prompt


class GrassRepair(CoreService):
    request_model = ImageRequest
    def __init__(self, app) -> None:
        super().__init__(
            client=model_factory.get_model(model_factory_selected),
            app=app,
            url_prefix="/generative-image-enhancement-grass-repair/predict",
        )

    def define_prompt(self) -> str:
        match model_factory_selected:
            case "openai":
                return openai_grass_repair_prompt
            case "flux":
                return flux_grass_repair_prompt
            case _:
                raise HTTPException(status_code=400, detail="Model not supported")

    async def flow(self, request: ImageRequest) -> ImageResponse | HTTPException:
        instance = request.instances[0]
        image_data = instance.image_url
        enhancement_level = instance.enhancement_level
        
        input_image = image_data
        
        prompt = self.define_prompt()

        response, token_usage = await self.client.image_edit(
            image=input_image, prompt=prompt
        )
        if isinstance(response, HTTPException):
            return response
        image_base64 = response
        return (
            ImageResponse(
                computer_vision={
                    "result": image_base64,
                    "service": "image-enhancement-grass-repair",
                }
            ),
            token_usage,
        )
    async def main(self, request: ImageRequest):
        parsed_request = self.request_model.model_validate(request)
        return await self._main(parsed_request)