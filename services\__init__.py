import importlib
import inspect
import os

from common.core.service import CoreService


def register_all(app):
    services_dir = os.path.dirname(__file__)

    for service_name in os.listdir(services_dir):
        service_path = os.path.join(services_dir, service_name)
        if os.path.isdir(service_path) and "flow.py" in os.listdir(service_path):
            module_path = f"services.{service_name}.flow"
            try:
                module = importlib.import_module(module_path)

                for _, cls in inspect.getmembers(module, inspect.isclass):
                    if issubclass(cls, CoreService) and cls is not CoreService:
                      cls(app)

            except Exception as e:
                print(f"Failed to register service '{service_name}': {e}")
