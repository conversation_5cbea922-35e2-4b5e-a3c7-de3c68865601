from typing import Type
from common.core.model import CoreModel
from common.utils.logger import logger
from common.utils.retry import retry
from common.utils.image_handler import ImageHandler

from openai import AsyncOpenAI
from dotenv import load_dotenv
from os import getenv
from common.core.schema import TokenUsage

load_dotenv()


class OpenAILoader(CoreModel):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.client = self.set_client()

    def set_client(self, openai_key: str = getenv("OPENAI_KEY")):
        if not openai_key:
            logger.error("Unable to load openai key")
            raise ValueError("You must set the OPENAI_KEY in your .env")
        return AsyncOpenAI(api_key=openai_key)

    @retry(max_retries=3, delay=0.5, default_return=None)
    async def image_edit(self, image, prompt, **kwargs):
        logger.info(f"Requesting image edit openai with prompt: {prompt[:20]}...")
        model = kwargs.get("model" , None) or getenv("IMAGE_GENERATION_MODEL", "gpt-image-1")
        if isinstance(image, list):
            image = [ImageHandler.base64_to_bytes(img) for img in image]
        else:
            image = ImageHandler.base64_to_bytes(image)
        quality = getenv("OPENAI_IMAGE_QUALITY", "low")
        response = await self.client.images.edit(
            model=model,
            image=image,
            prompt=prompt,
            n=kwargs.get("n", 1),
            size=kwargs.get("size", "auto"),
            quality=quality,
        )
        tokens = response.usage
        token_usage = TokenUsage(
            input_token=tokens.input_tokens_details.text_tokens,
            image_token=tokens.input_tokens_details.image_tokens,
            output_token=tokens.output_tokens,
            model_name=model,
            quality=quality,
        )
        return response.data[0].b64_json, token_usage
    
    @retry(max_retries=3, delay=0.5, default_return=None)
    async def chat_completion(self, structured_output: Type = None, *args, **kwargs):
        model = kwargs.get("model") or getenv("CHAT_COMPLETION_MODEL", "gpt-4o")
        if structured_output:

            response = await self.client.beta.chat.completions.parse(
                model=model,
                messages=kwargs.get("messages"),
                temperature=getenv("OPENAI_TEMP", None),
                top_p=getenv("top_p", None),
                response_format=structured_output,
            )
            usage = response.usage
            token_usage = TokenUsage(
                input_token=usage.prompt_tokens,
                output_token=usage.completion_tokens,
                model_name=model,
                image_token=0,
                quality=None,
            )
            return response.choices[0].message.parsed, token_usage
        else:
            response = await self.client.beta.chat.completions.parse(
                model=model,
                messages=kwargs.get("messages"),
                temperature=getenv("OPENAI_TEMP", 0.7),
                top_p=getenv("top_p", None),
            )
            usage = response.usage
            token_usage = TokenUsage(
                input_token=usage.prompt_tokens,
                output_token=usage.completion_tokens,
                model_name=model,
                image_token=0,
                quality=None,
            )
            # check the output if we dont provide structured output
            return response, token_usage
