# OpenAI prompt - detailed with enhancement level support
openai_light_correction_prompt = """Enhance this image by improving the lighting to make dark areas more visible and clear. 
Increase brightness in underexposed/dark areas {level_text} while maintaining natural look. 
Preserve all objects, furniture, and details exactly as they are. 
Don't add or remove any elements. Just correct the lighting to improve visibility in dark areas."""

# Flux prompt - improved following Kontext documentation guidelines
flux_light_correction_prompt = """Brighten dark and underexposed areas {level_text} while maintaining the exact same camera angle, position, and framing. Keep all objects, buildings, and structures exactly unchanged. Preserve natural lighting balance and color temperature. Maintain the original image quality and all unmodified elements exactly as they appear.""" 