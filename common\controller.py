from fastapi import APIRouter
from .database.run_migrations import run
from config import migration_folder
class GenericController():
    controller =True
    def __init__(self, app: APIRouter) -> None:
        run([migration_folder])
        self.app: APIRouter = app
        self.router: APIRouter = APIRouter()

    def add_api_route(self, path, endpoint, methods, dependencies=[]):
        self.router.add_api_route(
            path, endpoint, methods=methods, dependencies=dependencies)
