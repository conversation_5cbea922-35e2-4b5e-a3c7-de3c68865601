from typing import Tuple
from fastapi import HTTPException
from common.core.schema import TokenUsage
from common.models import model_factory
from common.utils.image_handler import <PERSON>Handler
from common.utils.logger import logger
from common.core.service import CoreService
from .schema import (
    VirtualStagingRequest,
    VirtualStagingResponse,
    VirtualStagingPreProcessing,
)
from config import model_factory_selected
from .prompts import *


class VirtualStaging(CoreService):

    request_model = VirtualStagingRequest

    def __init__(self, app) -> None:
        super().__init__(
            client=model_factory.get_model(model_factory_selected),
            app=app,
            url_prefix="/virtual-staging-refurnishing/predict",
        )

    def define_prompt(
        self, room_type: str, architecture_style: str, scene_type: str
    ) -> str:

        architecture_styles_dictionary = {
            "modern": "white, gray, black, blue, light-wood",
            "traditional": "beige, burgundy, green, navy, dark-brown",
            "countryside": "green, yellow, red, blue, brown",
            "coastal": "white, seafoam, beige, blue, gray",
            "contemporary": "greige, black, teal, blush, silver",
            "italian": "terracotta, olive, yellow, sienna, cream",
            "industrial": "gray, black, rust, red, metal",
            "scandinavian": "light-gray, white, blue, pink, light-wood",
            "wooden": "oak, walnut, pine, beige, clay"
        }

        match model_factory_selected:
            case "openai":
                if scene_type == "outdoor":
                    prompt = openai_outdoor_prompt.format(
                        architecture_style=architecture_style
                    )
                else:
                    prompt = openai_indoor_prompt.format(
                        room_type=room_type, architecture_style=architecture_style
                    )
                return prompt
            case "flux":
                architecture_style = architecture_style.lower()
                color_palette = architecture_styles_dictionary[architecture_style]
                furnitures: list = furnitures_dictionary[room_type][architecture_style]
                logger.debug(f"ROOM TYPE:{room_type}\nSTYLE:{architecture_style}\nFurnitures: {furnitures}")
                furnitures = ", ".join(furnitures)
                prompt = flux_prompt.format(
                    architecture_style=architecture_style, furnitures=furnitures, color_palette=color_palette
                )
                logger.debug(f"PROMPT:{prompt}")
                
                return prompt

            case _:
                raise HTTPException(status_code=400, detail="Model not supported")
        #     scene_type == "outdoor"
        #     prompt = openai_outdoor_prompt.format(architecture_style=architecture_style)
        # else:
        #     if not room_type:
        #         raise HTTPException(status_code=400, detail="Room type is required for indoor scenes")
        #     prompt = self.indoor_prompt.format(architecture_style=architecture_style, room_type=room_type)

    async def flow(
        self, request: VirtualStagingRequest
    ) -> VirtualStagingResponse | HTTPException:
        api_key = None
        client_api_key = request.client_api_key
        if client_api_key :
            api_key = client_api_key
            logger.info(f"USING CLIENT API KEY")
        input_image = request.image
        prompt = self.define_prompt(
            request.room_type, request.architecture_style, request.scene_type
        )
        response, token_usage = await self.client.image_edit(
            image=input_image, prompt=prompt, api_key=api_key
        )
        if isinstance(response, HTTPException):
            return response
        if not response: 
            return HTTPException(status_code=500, detail="Unable to generate an image")
        image_base64 = response
        return (
            VirtualStagingResponse(image=image_base64),
            [token_usage],
        )

    async def main(self, request: VirtualStagingRequest):
        parsed_request = self.request_model.model_validate(request)
        return await self._main(parsed_request)
