from sqlalchemy import select, insert
from sqlalchemy.ext.asyncio import AsyncSession
from typing import Dict, Any, Optional, List, Tuple
from common.database.models.requests import Request, Response, OpenAIToken
from common.database.db_connector import connector


class RequestRepository:
    def __init__(self):
        # Don't create a session at initialization
        self.connector = connector

    async def add_request(
        self, endpoint_call: str, request_data: Dict[str, Any]
    ) -> int:
        """
        Add a new request to the database and return its ID.
        """
        # Get a fresh session for each operation
        async with self.connector.get_session() as session:
            async with session.begin():
                # Create the request
                stmt = (
                    insert(Request)
                    .values(endpoint_call=endpoint_call, request=request_data)
                    .returning(Request.id)
                )

                result = await session.execute(stmt)
                request_id = result.scalar_one()

                return request_id

    async def add_response(
        self,
        request_id: int,
        status: int,
        response_data: Dict[str, Any],
        error_message: Optional[str] = None,
    ) -> int:
        """
        Add a response for a request.
        """
        # Get a fresh session for each operation
        async with self.connector.get_session() as session:
            async with session.begin():
                stmt = (
                    insert(Response)
                    .values(
                        request_id=request_id,
                        status=status,
                        response=response_data,
                        error_message=error_message,
                    )
                    .returning(Response.id)
                )

                result = await session.execute(stmt)
                response_id = result.scalar_one()

                return response_id

    async def add_token_usage(
        self,
        request_id: int,
        model_name: Optional[str] = None,
        quality: Optional[str] = None,
        input_token: Optional[int] = None,
        image_token: Optional[int] = None,
        output_token: Optional[int] = None,
    ) -> int:
        """
        Add token usage information for a request.
        """
        # Get a fresh session for each operation
        async with self.connector.get_session() as session:
            async with session.begin():
                stmt = (
                    insert(OpenAIToken)
                    .values(
                        request_id=request_id,
                        model_name=model_name,
                        quality=quality,
                        input_token=input_token,
                        image_token=image_token,
                        output_token=output_token,
                    )
                    .returning(OpenAIToken.id)
                )

                result = await session.execute(stmt)
                token_id = result.scalar_one()

                return token_id

    async def bulk_insert_requests(self, requests: List[Dict[str, Any]]) -> List[int]:
        """
        Bulk insert multiple requests and return their IDs.

        Args:
            requests: List of request dictionaries, each containing:
                - endpoint_call: The API endpoint that was called
                - request_data: The request data

        Returns:
            List of IDs for the newly created requests
        """
        if not requests:
            return []
        async with self.connector.get_session() as session:
            async with session.begin():
                # Prepare values for bulk insert
                values = [
                    {
                        "endpoint_call": req["endpoint_call"],
                        "request": req["request_data"],
                    }
                    for req in requests
                ]

                # Create the bulk insert statement with RETURNING
                stmt = insert(Request).values(values).returning(Request.id)

                # Execute and get results
                result = await session.execute(stmt)
                request_ids = result.scalars().all()

                await session.commit()
                return request_ids

    async def bulk_insert_responses(self, responses: List[Dict[str, Any]]) -> List[int]:
        """
        Bulk insert multiple responses and return their IDs.

        Args:
            responses: List of response dictionaries, each containing:
                - request_id: The ID of the associated request
                - status: HTTP status code
                - response_data: The response data
                - error_message: Optional error message

        Returns:
            List of IDs for the newly created responses
        """
        if not responses:
            return []
        async with self.connector.get_session() as session:
            async with session.begin():
                # Prepare values for bulk insert
                values = [
                    {
                        "request_id": resp["request_id"],
                        "status": resp["status"],
                        "response": resp["response_data"],
                        "error_message": resp.get("error_message"),
                    }
                    for resp in responses
                ]

                # Create the bulk insert statement with RETURNING
                stmt = insert(Response).values(values).returning(Response.id)

                # Execute and get results
                result = await session.execute(stmt)
                response_ids = result.scalars().all()

                await session.commit()
                return response_ids

    async def bulk_insert_token_usage(
        self, request_id,token_usages: List[Dict[str, Any]]
    ) -> List[int]:
        """
        Bulk insert multiple token usage records and return their IDs.

        Args:
            token_usages: List of token usage dictionaries, each containing:
                - request_id: The ID of the associated request
                - model_name: Optional name of the model used
                - quality: Optional quality setting used
                - input_token: Optional number of input tokens
                - image_token: Optional number of image tokens
                - output_token: Optional number of output tokens

        Returns:
            List of IDs for the newly created token usage records
        """
        if not token_usages:
            return []

        async with self.connector.get_session() as session:
            async with session.begin():
                # Prepare values for bulk insert
                values = [
                    {
                        "request_id": request_id,
                        "model_name": usage.get("model_name"),
                        "quality": usage.get("quality", ""),
                        "input_token": usage.get("input_token", 0),
                        "image_token": usage.get("image_token", 0),
                        "output_token": usage.get("output_token", 0),
                    }
                    for usage in token_usages
                ]

                # Create the bulk insert statement with RETURNING
                stmt = insert(OpenAIToken).values(values).returning(OpenAIToken.id)

                # Execute and get results
                result = await session.execute(stmt)
                token_ids = result.scalars().all()

                await session.commit()
            return token_ids

    async def get_request_by_id(self, request_id: int) -> Optional[Request]:
        async with self.connector.get_session() as session:
            async with session.begin():
                """Get a request by its ID with related responses and token usage"""
                stmt = select(Request).where(Request.id == request_id)
                result = await session.execute(stmt)
                return result.scalars().first()
