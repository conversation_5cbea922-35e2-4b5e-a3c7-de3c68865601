from typing import List, Optional
from pydantic import BaseModel
from common.core.schema import RequestSchema, ResponseSchema


class VirtualStagingRequest(RequestSchema):
    image: str
    room_type: Optional[str] = None
    scene_type: str
class VirtualStagingPreProcessing(BaseModel):
    is_relevant: bool
    room_type: str
    scene_type: str
    
    
class VirtualStagingResponse(BaseModel):
    image: str
