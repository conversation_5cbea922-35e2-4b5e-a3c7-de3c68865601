from fastapi import Fast<PERSON><PERSON>
from fastapi.middleware.cors import CORSMiddleware
from config import env
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

from services.light_correction.flow import LightCorrection
grass_repair = LightCorrection(app)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, timeout_keep_alive=300)