from typing import Type
from common.core.model import CoreModel
from common.utils.logger import logger
from common.utils.retry import retry
from fastapi import HTTPException
from dotenv import load_dotenv
from os import getenv
from common.core.schema import TokenUsage
import aiohttp
import asyncio
import base64

load_dotenv()


class FluxLoader(CoreModel):
    def __init__(self, *args, **kwargs):
        super().__init__(*args, **kwargs)
        self.client = self.set_client()

    def set_client(self, openai_key: str = getenv("BFL_API_KEY")):
        # if not openai_key:
        #     logger.error("Unable to load openai key")
        #     raise ValueError("You must set the OPENAI_KEY in your .env")
        return None

    @retry(max_retries=3, delay=0.5, default_return=None)
    async def image_edit(self, image, prompt, **kwargs):
        token_usage = TokenUsage(
            input_token=0,
            image_token=0,
            output_token=0,
            model_name="BFL",
            quality="",
        )

        async with aiohttp.ClientSession() as session:
            logger.info(f"Requesting image edit flux with prompt: {prompt[:20]}...")
            api_key = kwargs.get("api_key", None) or getenv("BFL_API_KEY")
            logger.debug(f"API Key: {api_key}")
            post_resp = await session.post(
                "https://api.bfl.ai/v1/flux-kontext-pro",
                headers={
                    "accept": "application/json",
                    "x-key": api_key,
                    "Content-Type": "application/json",
                },
                json={
                    "prompt": prompt,
                    "input_image": image,
                    # "prompt_upsampling": True,
                    # "safety_tolerance": 2
                },
            )
            request = await post_resp.json()
            request_id = request.get("id")
            logger.info(f"Request ID: {request_id}")
            if not request_id:
                logger.error(f"Invalid response: {request}")
                return None, None
        async with aiohttp.ClientSession() as session:
            attempts = 0
            while attempts <= 5:
                await asyncio.sleep(2)

                get_resp = await session.get(
                    "https://api.bfl.ai/v1/get_result",
                    headers={
                        "accept": "application/json",
                        "x-key": api_key,
                    },
                    params={"id": request_id},
                )
                result_data = await get_resp.json()
                status = result_data.get("status")
                logger.info(f"BFL Status: {status}")

                if status == "Ready":
                    final_result = result_data.get("result", {}).get("sample")
                    if final_result and final_result.startswith("http"):
                        logger.info(f"Final result: {final_result}")
                        result_image = await session.get(final_result)
                        image_bytes = await result_image.read()
                        final_result = base64.b64encode(image_bytes).decode("utf-8")
                        return final_result, token_usage
                    else:

                        attempts += 1
            raise HTTPException(status_code=500, detail="Failed to get final result")
            # elif status not in ["Processing", "Queued"]:
            #     logger.warning(f"Unexpected status or error: {result_data}")
            #     return None

    @retry(max_retries=3, delay=0.5, default_return=None)
    async def chat_completion(self, structured_output: Type = None, *args, **kwargs):
        return NotImplementedError("Chat completion is not implemented yet.")
