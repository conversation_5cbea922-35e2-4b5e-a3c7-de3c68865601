from fastapi import HTTPException
from common.models import model_factory
from common.utils.image_handler import ImageHandler
from common.core.service import CoreService
from .schema import ImageRequest, ImageResponse , ProductType, Product
from config import model_factory_selected


class ProductPlacement(CoreService):
    request_model = ImageRequest
    detector_prompt = """
I have the following product:

Product name: {product_name}  
Description: {product_description}

Based on this information, classify the product into **one of the following three categories only**:

1. Floor product  
2. Kitchen object  
3. Other

Reply with only the category name: "Floor product", "Kitchen object", or "Other".
"""

    prompt_flooring = 'Replace the floor in this room with the exact flooring shown in this product image, matching the material, color, and pattern precisely.'
    prompt_kitchen = 'Replace the kitchen countertops and cabinets with the exact surfaces and furniture shown in this product image, matching color, material, and style'
    direct_placement_prompt = """You are a professional product photographer and digital image composition expert. Your task is to seamlessly integrate the provided product into the room photo, creating a photorealistic result as if the product was physically present during the original shoot.

        Product: '{product_name}'

        ## Technical Requirements:
        1. **Seamless Integration**: Create a photorealistic composite where the '{product_name}' appears to be a natural part of the original scene.
        2. **Physical Accuracy**:
        - Proper scale: The product must be correctly sized relative to other objects in the room
        - Accurate perspective: Match the viewing angle of the room precisely
        - Correct shadows and reflections: Cast natural shadows and reflections consistent with existing light sources
        - Surface interactions: Create believable contact points with floors/surfaces

        3. **Lighting Consistency**:
        - Match the room's existing lighting temperature, direction, and intensity
        - Apply appropriate highlights and ambient occlusion
        - Ensure shadows fall in the correct direction based on visible light sources

        4. **Optimal Placement**:
        - Position the product in a logical, functional location within the room
        - Consider both aesthetic appeal and practical use case
        - Ensure the placement makes sense for the product type and room context

        ## Critical Constraints:
        - DO NOT modify ANY existing elements of the room (walls, windows, furniture, decor, etc.)
        - DO NOT alter the room's lighting conditions, color scheme, or overall atmosphere
        - DO NOT create unrealistic placement (e.g., floating products, impossible sizes)
        - DO NOT remove or replace any existing items in the room

        The final image must appear indistinguishable from a professional photograph taken with the product physically present in the room."""

    def __init__(self, app) -> None:
        super().__init__(
            client=model_factory.get_model(model_factory_selected),
            app=app,
            url_prefix="/api/product-placement",
        )

    async def flow(self, request: ImageRequest) -> ImageResponse | HTTPException:
        detector_text = self.detector_prompt.format(
            product_name=request.product_name,
            product_description=getattr(request, 'product_description', request.product_name)
        )
        room_image = request.room_image
        product_image = request.product_image
        messages = [
            {
                "role": "system",
                "content": "You are a helpful assistant that analyse images for real estate.",
            },
            {
                "role": "user",
                "content": [
                    {"type": "text", "text": detector_text},
                  
                ],
            },
        ]
        product_type, tokens = await self.client.chat_completion(
            messages=messages, structured_output=ProductType
        )
        # if 
        if product_type.product_type == Product.FLOORING:
            prompt = self.prompt_flooring
        elif product_type.product_type == Product.KITCHEN:
            prompt = self.prompt_kitchen
        else:
            prompt = self.direct_placement_prompt.format(product_name=request.product_name)
        response, token_usage = await self.client.image_edit(
            image=[room_image , product_image], prompt=prompt
        )
        if isinstance(response, HTTPException):
            return response
        image_base64 = response
        return (
            ImageResponse(
                image=image_base64,
            ),
            [token_usage, tokens],
        )
    async def main(self, request: ImageRequest):
        parsed_request = self.request_model.model_validate(request)
        return await self._main(parsed_request)