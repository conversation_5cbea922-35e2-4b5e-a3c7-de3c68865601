import time
import asyncio
from typing import Any, Callable, TypeVar, Union
from functools import wraps
from .logger import logger
from fastapi import HTTPException
F = TypeVar("F", bound=Callable[..., Any])

def retry(max_retries: int = 3, delay: float = 0.5 , default_return: Any = None):
    def decorator(func: F) -> F:
        if asyncio.iscoroutinefunction(func):
            @wraps(func)
            async def async_wrapper(*args, **kwargs):
                attempts = 0
                exception: Exception = None
                while attempts < max_retries:
                    try:
                        return await func(*args, **kwargs)
                    except Exception as e:
                        exception = e
                        attempts += 1
                        logger.warning(
                            f"Failed executing function: {func.__name__}, reason: {e}, retrying: ({attempts}/{max_retries})"
                        )
                        await asyncio.sleep(delay)
                if attempts == max_retries:
                    logger.error(
                        f"Failed executing function: {func.__name__}, reason: {exception}, max retries reached: ({attempts}/{max_retries})"
                    )
                    return HTTPException(status_code=500, detail=default_return) , default_return
                return exception
            return async_wrapper  # type: ignore
        else:
            @wraps(func)
            def sync_wrapper(*args, **kwargs):
                attempts = 0
                exception: Exception = None
                while attempts < max_retries:
                    try:
                        return func(*args, **kwargs)
                    except Exception as e:
                        exception = e
                        attempts += 1
                        logger.warning(
                            f"Failed executing function: {func.__name__}, reason: {e}, retrying: ({attempts}/{max_retries})"
                        )
                        time.sleep(delay)
                return exception
            return sync_wrapper  # type: ignore

    return decorator
