steps:
  # Build the main application image
  - name: gcr.io/cloud-builders/docker
    args:
      - build
      - '--tag'
      - >-
        ${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${PROJECT_ID}-ar-repo/${REPO_NAME}-app:${BRANCH_NAME}
      - '--file'
      - ./Dockerfile.app
      - .
    id: 'build-app'

  # Build the service image
  - name: gcr.io/cloud-builders/docker
    args:
      - build
      - '--tag'
      - >-
        ${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${PROJECT_ID}-ar-repo/${REPO_NAME}-service:${BRANCH_NAME}
      - '--file'
      - ./Dockerfile.svc
      - .
    id: 'build-service'

  # Push the main application image
  - name: gcr.io/cloud-builders/docker
    args:
      - push
      - >-
        ${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${PROJECT_ID}-ar-repo/${REPO_NAME}-app:${BRANCH_NAME}
    waitFor: ['build-app']
    id: 'push-app'

  # Push the service image
  - name: gcr.io/cloud-builders/docker
    args:
      - push
      - >-
        ${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${PROJECT_ID}-ar-repo/${REPO_NAME}-service:${BRANCH_NAME}
    waitFor: ['build-service']
    id: 'push-service'

images:
  - >-
    ${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${PROJECT_ID}-ar-repo/${REPO_NAME}-app:${BRANCH_NAME}
  - >-
    ${LOCATION}-docker.pkg.dev/${PROJECT_ID}/${PROJECT_ID}-ar-repo/${REPO_NAME}-service:${BRANCH_NAME}

options:
  substitutionOption: ALLOW_LOOSE
  logging: CLOUD_LOGGING_ONLY
  machineType: 'E2_HIGHCPU_8'
  diskSizeGb: 100

substitutions:
  _LOCATION: 'us-central1'
  _PROJECT_ID: '${PROJECT_ID}'
  _REPO_NAME: 'proptexx-staging'

timeout: '1200s'
