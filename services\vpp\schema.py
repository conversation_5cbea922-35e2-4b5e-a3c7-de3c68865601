from typing import List, Optional
from pydantic import BaseModel
from common.core.schema import RequestSchema, ResponseSchema


from pydantic import BaseModel, Field
from typing import Optional

class ImageRequest(RequestSchema):
    image: str

class PreProcessing(BaseModel):
    is_relevant: bool
    room_type: str
    


class Item(BaseModel):
    id: int
    name: str
    description: str
    category: str


class GenereativeMessage(BaseModel):
    prompt: str
    items: list[Item]
    
class ImageGenerationResponse(BaseModel):
    image: str
    items: Optional[list[Item]] = Field(
        default=[], description="List of items used to generate the image"
    )


class PostProcessing(BaseModel):
    items: list[Item] = Field(
        description="List of items used in the generated the image"
    )
    
