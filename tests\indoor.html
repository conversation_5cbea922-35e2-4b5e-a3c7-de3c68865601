<!DOCTYPE html>
<html lang="en">

<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Image Processor with Style Variations</title>
    <style>
        body {
            font-family: sans-serif;
            margin: 0;
            padding: 20px;
            background-color: #111;
            color: #fff;
        }

        #container {
            max-width: 1200px;
            margin: 0 auto;
        }

        h1 {
            color: #cc66ff;
            text-align: center;
        }

        button {
            background-color: #33ccff;
            color: #000;
            padding: 10px 20px;
            border: none;
            cursor: pointer;
            font-size: 16px;
            border-radius: 5px;
            margin-right: 10px;
        }

        button:hover {
            background-color: #66ffcc;
        }

        table {
            width: 100%;
            border-collapse: collapse;
            border: 2px solid #cc66ff;
        }

        th,
        td {
            border: 1px solid #ddd;
            padding: 8px;
            text-align: center;
            color: #eee;
        }

        th {
            background-color: #333;
            color: #33ccff;
        }

        img {
            max-width: 150px;
            max-height: 150px;
            cursor: pointer;
        }

        .fullscreen-image {
            position: fixed;
            top: 0;
            left: 0;
            width: 100%;
            height: 100%;
            object-fit: contain;
            background-color: rgba(0, 0, 0, 0.9);
            z-index: 1000;
        }

        #logs {
            margin-top: 20px;
            padding: 10px;
            border: 1px solid #cc66ff;
            height: 150px;
            overflow-y: scroll;
            color: #eee;
        }
    </style>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/jszip/3.10.1/jszip.min.js"></script>
    <script src="https://cdnjs.cloudflare.com/ajax/libs/FileSaver.js/2.0.5/FileSaver.min.js"></script>
</head>

<body>

    <div id="container">
        <h1>Image Style Processor</h1>
        <button id="submitButton">Process Images</button>
        <button id="downloadAll">Download All Images</button>
        <table id="imageTable">
            <thead>
                <tr>
                    <th>Original Image</th>
                    <th>Coastal</th>
                    <th>Scandinavian</th>
                    <th>Italian</th>
                    <th>Industrial</th>
                    <th>Wooden</th>
                </tr>
            </thead>
            <tbody>
                <!-- Image rows will be inserted here -->
            </tbody>
        </table>

        <h2>Logs</h2>
        <div id="logs"></div>
    </div>

    <script>
        const imageTable = document.getElementById('imageTable').getElementsByTagName('tbody')[0];
        const submitButton = document.getElementById('submitButton');
        const downloadButton = document.getElementById('downloadAll');
        const logs = document.getElementById('logs');

        // const images = [
        //     'bathroom0.jpg', 
        //     'bedroom0.jpg', 
        //     // 'hallway0.jpg', 
        //     // 'patio0.jpg', 
        //     'bathroom1.jpg',
        //     'bedroom1.jpg', 
        //     // 'hallway1.jpg', 
        //     'kitchen0.jpg', 
        //     // 'patio1.jpg', 
        //     // 'home gym0.jpg',
        //     'living room0.jpg', 
        //     'digning room0.jpg', 
        //     'living room1.jpg', 
        //     // 'gaming room0.jpeg',
        //     // 'gaming room1.jpeg', 
        //     // 'gaming room2.jpeg', 
        //     // 'home office0.jpeg', 
        //     // 'home office1.jpeg',
        //     'digining room1.jpg'
        // ];
        const images = [
        // 🛁 Bathrooms
        // "appartements/listing1/bathroom.jpg",
        // "appartements/listing2/bathroom.jpg",
        // "appartements/listing2/bathroom2.jpg",
        // "appartements/listing4/bathroom.jpg",
        // "appartements/listing4/bathroom2.jpg",
        "appartements/listing5/bathroom.jpg",
        "appartements/listing6/bathroom.jpg",

        // // 🛏️ Bedrooms
        "appartements/listing1/bedroom.jpg",
        "appartements/listing2/bedroom.jpg",
        "appartements/listing3/bedroom.jpg",
        "appartements/listing3/bedroom2.jpg",
        "appartements/listing4/bedroom.jpg",
        "appartements/listing5/bedroom.jpg",
        "appartements/listing5/bedroom2.jpg",
        "appartements/listing6/bedroom.jpg",
        "appartements/listing6/bedroom2.jpg",

        // // // 🍽️ Kitchens
        // "appartements/listing1/kitchen.jpg",
        // "appartements/listing2/kitchen.jpg",
        // "appartements/listing3/kitchen.jpg",
        // "appartements/listing4/kitchen.jpg",
        "appartements/listing5/kitchen.jpg",
        "appartements/listing6/kitchen.jpg",
        // // "kitchen0.jpg"

        // // // 🛋️ Living Rooms
        // "appartements/listing1/livingroom.jpg",
        // "appartements/listing2/livingroom.jpg",
        // "appartements/listing3/livingroom.jpg",
        // "appartements/listing4/livingroom.jpg",
        // "appartements/listing5/livingroom.jpg",
        "appartements/listing6/livingroom.jpg",

        // "appartements/listing1/livingroom2.jpg",
        // "appartements/listing2/livingroom2.jpg",
        "appartements/listing3/livingroom2.jpg"
        ];

        const architectureStyles = ["coastal", "scandinavian", "italian", "industrial", "wooden"];
        // const architectureStyles = ["coastal",  "italian", "industrial"];
        // const architectureStyles = ["coastal", ];

        function showFullscreen(imgSrc) {
            const fullscreenDiv = document.createElement('div');
            fullscreenDiv.classList.add('fullscreen-image');
            fullscreenDiv.style.backgroundImage = `url(${imgSrc})`;
            fullscreenDiv.style.backgroundRepeat = 'no-repeat';
            fullscreenDiv.style.backgroundPosition = 'center';
            fullscreenDiv.style.backgroundSize = 'contain';
            document.body.appendChild(fullscreenDiv);

            document.addEventListener('keydown', function (e) {
                if (e.key === 'Escape') {
                    document.body.removeChild(fullscreenDiv);
                }
            }, { once: true });
        }

        function attachImageEvents() {
            const images = document.querySelectorAll('#imageTable img');
            images.forEach(img => {
                img.addEventListener('click', function () {
                    showFullscreen(this.src);
                });
            });
        }

        images.forEach(imageName => {
            const row = imageTable.insertRow();
            let cell = row.insertCell();
            cell.innerHTML = `<img src="images/${imageName}" alt="${imageName}">`;

            architectureStyles.forEach(() => {
                cell = row.insertCell();
                cell.innerHTML = "Processing...";
                cell.classList.add("result-cell");
            });
        });

        attachImageEvents();

        submitButton.addEventListener('click', async () => {
            submitButton.disabled = true;
            logMessage("Starting image processing...");

            const allPromises = [];

            for (let i = 0; i < images.length; i++) {
                const imageName = images[i];
                const row = imageTable.rows[i];

                for (let j = 0; j < architectureStyles.length; j++) {
                    const architectureStyle = architectureStyles[j];
                    const resultCell = row.cells[j + 1];

                    const promise = (async () => {
                        try {
                            const base64Image = await imageToBase64(`images/${imageName}`);
                            const apiResponse = await sendToAPI(imageName, base64Image, architectureStyle);

                            if (apiResponse && apiResponse.data.image) {
                                resultCell.innerHTML = `<img src="data:image/jpeg;base64,${apiResponse.data.image}" alt="${architectureStyle}">`;
                                logMessage(`Processed ${imageName} with ${architectureStyle}: Success`);
                            } else {
                                resultCell.innerHTML = "Error: Invalid API Response";
                                logMessage(`Error processing ${imageName} with ${architectureStyle}: Invalid API Response`);
                            }
                        } catch (error) {
                            resultCell.innerHTML = "Error";
                            logMessage(`Error processing ${imageName} with ${architectureStyle}: ${error}`);
                        }
                    })();

                    allPromises.push(promise);
                }
            }

            await Promise.all(allPromises);
            attachImageEvents();
            submitButton.disabled = false;
            logMessage("Image processing complete.");
        });

        downloadButton.addEventListener('click', async () => {
            const zip = new JSZip();

            for (let i = 0; i < images.length; i++) {
                const row = imageTable.rows[i];
                const originalImg = row.cells[0].querySelector('img');
                const originalName = originalImg.alt;
                const baseName = originalName.substring(0, originalName.lastIndexOf('.')).toLowerCase();

                // Determine room type
                let roomType = baseName;
                if (baseName.includes('bathroom')) roomType = 'bathroom';
                else if (baseName.includes('bedroom')) roomType = 'bedroom';
                else if (baseName.includes('kitchen')) roomType = 'kitchen';
                else if (baseName.includes('living room')) roomType = 'living room';
                else if (baseName.includes('hallway')) roomType = 'hallway';
                else if (baseName.includes('patio')) roomType = 'patio';
                else if (baseName.includes('digning') || baseName.includes('dining')) roomType = 'dining room';
                else if (baseName.includes('gaming')) roomType = 'gaming room';
                else if (baseName.includes('office')) roomType = 'home office';
                else if (baseName.includes('gym')) roomType = 'home gym';

                // Add original image
                const originalBase64 = await imageToBase64(originalImg.src);
                zip.file(`generated_images/${roomType}/${originalName}`, originalBase64, { base64: true });

                // Add generated versions
                for (let j = 0; j < architectureStyles.length; j++) {
                    const style = architectureStyles[j];
                    const generatedImg = row.cells[j + 1].querySelector('img');
                    if (generatedImg) {
                        const generatedBase64 = generatedImg.src.split(',')[1];
                        const generatedFileName = `${baseName}_${style}.jpg`;
                        zip.file(`generated_images/${roomType}/${generatedFileName}`, generatedBase64, { base64: true });
                    }
                }
            }

            zip.generateAsync({ type: "blob" }).then(content => {
                saveAs(content, "generated_images.zip");
            });
        });
        
        
        async function imageToBase64(imagePath) {
            return new Promise((resolve, reject) => {
                const img = new Image();
                img.crossOrigin = 'anonymous';
                img.onload = () => {
                    const canvas = document.createElement('canvas');
                    canvas.width = img.width;
                    canvas.height = img.height;
                    const ctx = canvas.getContext('2d');
                    ctx.drawImage(img, 0, 0);
                    const dataURL = canvas.toDataURL('image/jpeg');
                    resolve(dataURL.substring(dataURL.indexOf(',') + 1));
                };
                img.onerror = reject;
                img.src = imagePath;
            });
        }

        async function sendToAPI(imageName, base64Image, architectureStyle) {
            const apiURL = 'http://localhost:8000/virtual-staging-refurnishing/predict';

            const baseName = imageName.substring(0, imageName.lastIndexOf('.')).toLowerCase();
            let roomType = baseName;
            if (baseName.includes('bathroom')) roomType = 'bathroom';
            else if (baseName.includes('bedroom')) roomType = 'bedroom';
            else if (baseName.includes('kitchen')) roomType = 'kitchen';
            else if (baseName.includes('living')) roomType = 'living room';
            else if (baseName.includes('hallway')) roomType = 'hallway';
            else if (baseName.includes('patio')) roomType = 'patio';
            else if (baseName.includes('digning') || baseName.includes('dining') || baseName.includes('digining')) roomType = 'dining room';
            else if (baseName.includes('gaming')) roomType = 'gaming room';
            else if (baseName.includes('office')) roomType = 'home office room';
            else if (baseName.includes('gym')) roomType = 'home gym';

            const payload = {
                image: base64Image,
                architecture_style: architectureStyle,
                room_type: roomType,
                scene_type: "indoor"
            };

            const response = await fetch(apiURL, {
                method: 'POST',
                headers: { 'Content-Type': 'application/json' },
                body: JSON.stringify(payload)
            });

            if (!response.ok) {
                throw new Error(`API request failed with status ${response.status}`);
            }

            return await response.json();
        }

        function logMessage(message) {
            const logEntry = document.createElement('p');
            logEntry.textContent = message;
            logs.appendChild(logEntry);
            logs.scrollTop = logs.scrollHeight;
        }
    </script>

</body>

</html>