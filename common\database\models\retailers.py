from sqlalchemy import <PERSON>um<PERSON>, Inte<PERSON>, String, ForeignKey,Text
from sqlalchemy.orm import relationship
from .___base_model import Base
from sqlalchemy.orm import mapped_column, Mapped, relationship
from sqlalchemy import Date, String, Enum, Integer, ForeignKey
from sqlalchemy.dialects.postgresql import VARCHAR

class RoomType(Base):
  __tablename__ = "room_type"
  id :Mapped[int] = mapped_column(
    Integer, primary_key=True, autoincrement=True
  )
  name: Mapped[str] = mapped_column(VARCHAR(255), nullable=False)
  

class Category(Base):
  __tablename__ = "category"
  id :Mapped[int] = mapped_column(
    Integer, primary_key=True, autoincrement=True
  )
  room_type_id: Mapped[int] = mapped_column(
    Integer, ForeignKey("room_type.id"), nullable=False
  )
  name: Mapped[str] = mapped_column(VARCHAR(255), nullable=False)  
  
class Item(Base):
  __tablename__ = "item"
  id :Mapped[int] = mapped_column(
    Integer, primary_key=True, autoincrement=True
  )
  category_id: Mapped[int] = mapped_column(
    Integer, ForeignKey("category.id"), nullable=False
  )
  name: Mapped[str] = mapped_column(VARCHAR(255), nullable=False)
  description: Mapped[str] = mapped_column(Text, nullable=True)
  image_url: Mapped[str] = mapped_column(VARCHAR(255), nullable=True)
  item_link: Mapped[str] = mapped_column(VARCHAR(255), nullable=True)
  
