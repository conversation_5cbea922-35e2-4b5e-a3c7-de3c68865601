from .openai_model import OpenAILoader
from .flux_model import FluxLoader

class ModelFactory:
    @staticmethod
    def get_model(model_type: str):
        match model_type:
            case "openai":
                return OpenAILoader()
            case "flux":
                return FluxLoader()
            case _:
                raise ValueError(f"Unsupported model type: {model_type}")
        # if model_type == "openai":
        #     return OpenAILoader()
        # elif model_type == "flux":
        #     return FluxLoader()
        # else:
        #     raise ValueError(f"Unsupported model type: {model_type}")


model_factory = ModelFactory()
