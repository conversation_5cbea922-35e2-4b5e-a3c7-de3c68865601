from typing import List, Optional
from pydantic import BaseModel
from common.core.schema import RequestSchema, ResponseSchema
class ImageInstance(BaseModel):
    image_url: str
    enhancement_level: float = 0.5
    apply_low_light: Optional[bool] = True
    simple_prompt: Optional[bool] = False

class ImageRequest(RequestSchema):
    instances: List[ImageInstance]

class ImageResponse(BaseModel):
    computer_vision: dict 