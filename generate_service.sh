#!/bin/bash
read -p "Enter the service name: " service_name

# Use grep to enforce only a-z, 0-9, and underscore
if echo "$service_name" | LC_ALL=C grep -qE '^[a-z0-9_]+$'; then
    if ls ./services | grep -q "^$service_name$"; then
        echo "Error: Service '$service_name' already exists in ./src/services."
        exit 1
    fi

    echo "Valid service name."
else
    echo "Invalid service name. It must be lowercase and contain only letters, digits, or underscores."
    exit 1
fi

class_name=$(echo "$service_name" | awk -F'_' '{
        for (i = 1; i <= NF; i++) {
            printf("%s", toupper(substr($i,1,1)) substr($i,2));
        }
    }')

echo "ClassName: $class_name"
mkdir -p ./services/$service_name
api_name="${service_name//_/-}"
cat <<EOF > ./services/$service_name/app.py

from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from config import env
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

from services.$service_name.flow import $class_name
grass_repair = $class_name(app)

if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, timeout_keep_alive=300)
EOF


cat <<EOF > ./services/$service_name/flow.py

from fastapi import HTTPException
from common.core.schema import TokenUsage
from common.models import model_factory
from common.utils.image_handler import ImageHandler
from common.utils.logger import logger
from common.core.service import CoreService
from .schema import ${class_name}Request, ${class_name}Response
from config import model_factory_selected


class ${class_name}(CoreService):
    request_model = ${class_name}Request
    def __init__(self, app) -> None:
        super().__init__(
            client=model_factory.get_model(model_factory_selected),
            app=app,
            url_prefix="/${api_name}/predict",
        )
    async def flow(self, request: ${class_name}Request) ->  ${class_name}Response | HTTPException:
        
        return (
             ${class_name}Response(...),
            [token_usage ],
        )
    async def main(self, request: ${class_name}Request):
        parsed_request = self.request_model.model_validate(request)
        return await self._main(parsed_request)
EOF


cat <<EOF > ./services/$service_name/schema.py
from pydantic import BaseModel
from common.core.schema import RequestSchema, ResponseSchema


class ${class_name}Request(RequestSchema):
    ...
    
class ${class_name}Response(BaseModel):
    ...
EOF