# OpenAI prompt - detailed and comprehensive
openai_sky_replacement_prompt = """Enhance this image while strictly preserving all objects, buildings, and structures:
1. If sky is present, enhance only the sky area while keeping it natural looking
2. If no sky is present, enhance overall image quality while preserving all elements
3. DO NOT modify any buildings, furniture, objects, or structures
4. Keep all architectural elements exactly as they are
5. Preserve all man-made objects and their details
6. Maintain original lighting conditions
7. Do not add or remove any elements
8. If enhancing sky, keep it looking natural and not artificial
9. Preserve all edges and boundaries between different elements
10. Keep all textures and materials unchanged"""

# Flux prompt - short and simple
flux_sky_replacement_prompt = """Replace the sky area with dramatic blue sky and white clouds while maintaining the exact same camera angle, position, and framing. Keep all buildings, objects, and architectural elements exactly unchanged. Preserve original lighting conditions on all structures. Maintain the original image quality and all unmodified elements exactly as they appear."""