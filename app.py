import importlib.util
import inspect
import os
from fastapi import FastAPI
from fastapi.middleware.cors import CORSMiddleware
from services import register_all
from config import env
app = FastAPI()
app.add_middleware(
    CORSMiddleware,
    allow_origins=["*"],
    allow_credentials=True,
    allow_methods=["*"],
    allow_headers=["*"],
)

register_all(app)
if env != "production":
    from ui import router as ui_router
    app.include_router(ui_router)
if __name__ == "__main__":
    import uvicorn
    uvicorn.run(app, host="0.0.0.0", port=8000, timeout_keep_alive=300)