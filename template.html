<!DOCTYPE html>
<html lang="en">
<head>
  <meta charset="UTF-8" />
  <title>🧪 AI Service Debugger</title>
  <style>
    @import url('https://fonts.googleapis.com/css2?family=Orbitron:wght@500&display=swap');
  
    * {
      box-sizing: border-box;
    }
  
    body {
      font-family: 'Orbitron', sans-serif;
      margin: 0;
      padding: 0;
      background-color: #0d1117;
      color: #e2e8f0;
    }
  
    .container {
      max-width: 960px;
      margin: auto;
      padding: 2rem;
    }
  
    h2 {
      font-size: 1.8rem;
      color: #f472b6;
      text-shadow: 0 0 6px #f472b6;
      margin-bottom: 1.5rem;
    }
  
    h3 {
      margin-top: 2rem;
      color: #60a5fa;
      text-shadow: 0 0 4px #60a5fa;
    }
  
    label {
      font-weight: 600;
      margin-bottom: 0.5rem;
      display: block;
      color: #e2e8f0;
    }
  
    select, input[type="text"], input[type="number"], input[type="file"] {
      width: 100%;
      padding: 0.6rem;
      margin-bottom: 1.2rem;
      background-color: #1e293b;
      color: #f8fafc;
      border: 1px solid #7dd3fc;
      border-radius: 6px;
    }
  
    input::file-selector-button {
      background: #1e40af;
      color: white;
      border: none;
      padding: 0.4rem 0.8rem;
      border-radius: 4px;
      cursor: pointer;
    }
  
    button {
      background: linear-gradient(90deg, #ec4899, #6366f1);
      color: white;
      padding: 0.7rem 1.4rem;
      font-size: 1rem;
      border: none;
      border-radius: 8px;
      cursor: pointer;
      box-shadow: 0 0 12px #f472b6;
      transition: transform 0.2s ease;
    }
  
    button:hover {
      transform: scale(1.05);
    }
  
    fieldset {
      padding: 1rem;
      border: 1px dashed #64748b;
      border-radius: 6px;
      margin-bottom: 1rem;
    }
  
    .form-card {
      background: rgba(17, 24, 39, 0.8);
      padding: 2rem;
      border-radius: 12px;
      border: 1px solid #334155;
      box-shadow: 0 0 20px rgba(255, 0, 255, 0.15);
      margin-bottom: 2rem;
    }
  
    .status {
      margin-top: 1rem;
      font-weight: bold;
      color: #38bdf8;
    }
  
    #spinner {
      display: inline-block;
      width: 20px;
      height: 20px;
      border: 3px solid #334155;
      border-top: 3px solid #f472b6;
      border-radius: 50%;
      animation: spin 0.6s linear infinite;
      margin-left: 10px;
      vertical-align: middle;
    }
  
    @keyframes spin {
      to { transform: rotate(360deg); }
    }
  
    #preview {
      display: flex;
      flex-wrap: wrap;
      gap: 1.5rem;
      margin-top: 2rem;
    }
  
    #preview img {
      border-radius: 10px;
      max-width: 360px;
      box-shadow: 0 0 12px #60a5fa;
    }
  
    pre, textarea {
      background: #1e293b;
      color: #d1d5db;
      padding: 1rem;
      border-radius: 8px;
      font-family: monospace;
      width: 100%;
      max-width: 700px;
      overflow-x: auto;
      border: 1px solid #475569;
    }
  
    summary {
      cursor: pointer;
      font-weight: 600;
      margin-top: 1rem;
      color: #fbbf24;
    }
  </style>
  
</head>
<body>
  <div class="container">
    <h2>🧪 AI Service Debugger</h2>

    <div class="form-card">
      <label for="endpoint">Select Endpoint</label>
      <select id="endpoint"></select>

      <form id="dynamicForm"></form>
      <button type="button" onclick="submitForm()">Submit</button>
      <span id="spinner" style="display:none;"></span>
      <div id="status" class="status"></div>
    </div>

    <h3>📷 Preview</h3>
    <div id="preview"></div>
  </div>

  <script>
    const spec = {{SPEC_JSON}};
    const form = document.getElementById("dynamicForm");

    function resolveRef(ref) {
      return spec.components.schemas[ref.split("/").pop()];
    }

    function loadEndpoints() {
      const select = document.getElementById("endpoint");
      Object.entries(spec.paths).forEach(([path, val]) => {
        if ((path.endsWith("/predict") || path.startsWith("/api/")) && val.post?.requestBody) {
          const opt = document.createElement("option");
          opt.value = path;
          opt.innerText = path;
          select.appendChild(opt);
        }
      });
      select.onchange = () => buildForm(select.value);
      buildForm(select.value);
    }

    function buildForm(path) {
      form.innerHTML = "";
      const schemaRef = spec.paths[path].post.requestBody.content["application/json"].schema.$ref;
      const schema = resolveRef(schemaRef);

      Object.entries(schema.properties).forEach(([key, val]) => {
        const wrapper = document.createElement("div");
        if (val.type === "array" && val.items?.$ref) {
          const nested = resolveRef(val.items.$ref);
          const container = document.createElement("div");
          container.id = key + "_container";

          const addBtn = document.createElement("button");
          addBtn.type = "button";
          addBtn.innerText = "Add " + key;
          addBtn.style.marginBottom = "10px";
          addBtn.onclick = () => {
            const group = document.createElement("fieldset");
            Object.entries(nested.properties).forEach(([subkey, subval]) => {
              const label = document.createElement("label");
              label.innerText = subkey;
              const input = document.createElement("input");
              input.name = key + "__" + subkey;
              input.type = subkey.includes("image") ? "file" : (subval.type === "number" ? "number" : "text");
              if (input.type === "file") input.accept = "image/*";
              group.appendChild(label);
              group.appendChild(input);
            });
            container.appendChild(group);
          };
          wrapper.appendChild(addBtn);
          wrapper.appendChild(container);
        } else {
          const label = document.createElement("label");
          label.innerText = key;
          const input = document.createElement("input");
          input.name = key;
          input.type = key.includes("image") ? "file" : (val.type === "number" ? "number" : "text");
          if (input.type === "file") input.accept = "image/*";
          wrapper.appendChild(label);
          wrapper.appendChild(input);
        }
        form.appendChild(wrapper);
      });
    }

    async function toBase64(file) {
      return new Promise((resolve, reject) => {
        const reader = new FileReader();
        reader.onload = () => resolve(reader.result.split(',')[1]);
        reader.onerror = reject;
        reader.readAsDataURL(file);
      });
    }

    async function submitForm() {
      const endpoint = document.getElementById("endpoint").value;
      const data = {};
      const preview = document.getElementById("preview");
      const status = document.getElementById("status");
      const spinner = document.getElementById("spinner");

      preview.innerHTML = "";
      spinner.style.display = "inline-block";
      status.innerText = "Sending request...";

      const fileInputs = form.querySelectorAll("input[type='file']");
      for (const input of fileInputs) {
        if (!input.files[0]) continue;
        const base64 = await toBase64(input.files[0]);
        input.dataset.base64 = base64;
        if (!input.name.includes("__")) data[input.name] = base64;

        const img = document.createElement("img");
        img.src = "data:image/png;base64," + base64;
        img.title = "Input";
        preview.appendChild(img);
      }

      const normalInputs = form.querySelectorAll("input[type='text'], input[type='number']");
      for (const input of normalInputs) {
        if (!input.name.includes("__"))
          data[input.name] = input.type === "number" ? parseFloat(input.value) : input.value;
      }

      const groups = form.querySelectorAll("div[id$='_container']");
      groups.forEach(container => {
        const key = container.id.replace("_container", "");
        data[key] = [];
        container.querySelectorAll("fieldset").forEach(group => {
          const obj = {};
          group.querySelectorAll("input").forEach(input => {
            const k = input.name.split("__")[1];
            obj[k] = input.type === "file" ? input.dataset.base64 : input.value;
          });
          data[key].push(obj);
        });
      });

      const res = await fetch(endpoint, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify(data)
      });

      const out = await res.json();
      spinner.style.display = "none";

      if (!out.data) return status.innerText = "❌ No data key in response.";

      // const raw = document.createElement("details");
      // raw.innerHTML = `<summary>📦 Raw JSON</summary><textarea>${JSON.stringify(out, null, 2)}</textarea>`;
      // preview.appendChild(raw);

      function walk(obj, path = "") {
        for (let key in obj) {
          const val = obj[key];
          const fullKey = path ? path + "." + key : key;
          if (typeof val === "string") {
            const looksImage = val.length > 100 && /^[A-Za-z0-9+/=]+$/.test(val);
            if (key.toLowerCase().endsWith("image") || (key.toLowerCase().endsWith("result") && looksImage)) {
              const img = document.createElement("img");
              img.src = "data:image/png;base64," + val;
              img.title = fullKey;
              preview.appendChild(img);
            }
          } else if (typeof val === "object") {
            walk(val, fullKey);
          }
        }
      }

      walk(out.data);
      status.innerText = "✅ Response loaded";
    }

    loadEndpoints();
  </script>
</body>
</html>
