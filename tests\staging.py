import os
import requests
import json
import base64
from pathlib import Path
from rich.console import Console
from rich.progress import Progress, SpinnerColumn, TextColumn, TimeElapsedColumn
import time
import csv
import signal
import sys

# Initialize Rich console
console = Console()

# Create CSV file for logging
csv_file = "openai_staging_results.csv"
with open(csv_file, "w", newline="") as f:
    writer = csv.writer(f)
    writer.writerow(
        ["input_path", "output_path", "room_type", "style", "duration", "status"]
    )

# Flag to handle graceful shutdown
running = True


def signal_handler(sig, frame):
    global running
    console.print(
        "\n[yellow]Shutting down gracefully. Completing current task...[/yellow]"
    )
    running = False


signal.signal(signal.SIGINT, signal_handler)

# Map folder names to API room types
room_type_mapping = {
    "living_room": "living room",
    "bedroom": "bedroom",
    "bathroom": "bathroom",
    "kitchen": "kitchen",
}

# Map folder names to API architecture styles
style_mapping = {
    "coastal": "coastal",
    "industrial": "industrial",
    "modern": "modern",
    "scandinavian": "scandinavian",
}


# Function to convert image to base64
def image_to_base64(image_path):
    with open(image_path, "rb") as img_file:
        return base64.b64encode(img_file.read()).decode("utf-8")


# Base directory
# base_dir = Path("stagin-refurnishing/staging")
base_dir = Path("/home/<USER>/Desktop/workspace/proptexx/ai-dev/proptexx-staging-productplacement/stagin-refurnishing/staging")

console.print(
    f"[bold green]Starting processing of staging folder: {base_dir}[/bold green]"
)

# Find all input.jpg files in the staging folder
input_files = []
for room_type_dir in base_dir.iterdir():
    if room_type_dir.is_dir():
        room_type = room_type_dir.name
        for style_dir in room_type_dir.iterdir():
            if style_dir.is_dir():
                style = style_dir.name
                for file in style_dir.glob("*_input.jpg"):
                    input_files.append((file, room_type, style))

console.print(f"[bold blue]Found {len(input_files)} input files to process[/bold blue]")

with Progress(
    SpinnerColumn(),
    TextColumn("[progress.description]{task.description}"),
    TimeElapsedColumn(),
    console=console,
) as progress:
    task = progress.add_task(
        f"[cyan]Processing input files...[/cyan]", total=len(input_files)
    )

    for input_file, room_type, style in input_files:
        if not running:
            break

        progress.update(task, description=f"[cyan]Processing {input_file.name}[/cyan]")

        # Create output path
        output_file = (
            input_file.parent
            / f"{input_file.stem.replace('_input', '')}_output_F_Kontext.jpg"
        )

        # Get API room type and style
        api_room_type = room_type_mapping.get(room_type, "LIVING_ROOM")
        api_style = style_mapping.get(style, "MODERN")

        start_time = time.time()
        status = "failed"

        try:
            # Convert image to base64
            console.print(f"  [blue]Converting image to base64: {input_file}[/blue]")
            image_base64 = image_to_base64(input_file)

            # Create payload with base64_image instead of image_url
            payload = {
                "image": image_base64,
                "room_type": api_room_type,
                "architecture_style": api_style,
                "scene_type": "indoor",
            }

            # Set up request parameters
            headers = {
                "Content-Type": "application/json",
                "accept": "application/json",
            }

            console.print(
                f"  [magenta]Sending request for {input_file.name} with room_type={api_room_type}, style={api_style}[/magenta]"
            )

            # Send request to API
            response = requests.post(
                "http://localhost:8000/virtual-staging-refurnishing/predict",
                json=payload,
                headers=headers,
                timeout=120,
                verify=False,
            )
            if response.status_code == 200:
                result = response.json()
                result = result["data"]
                if "image" in result and result["image"]:
                    # Save the image
                    image_data = result["image"]

                    # If the image is base64 encoded, decode it
                    if isinstance(image_data, str):
                        # Extract the base64 part
                        # image_data = image_data.split(",")[1]
                        image_bytes = base64.b64decode(image_data)

                        with open(output_file, "wb") as f:
                            f.write(image_bytes)
                    else:
                        # If it's binary data
                        with open(output_file, "wb") as f:
                            f.write(image_data)

                    status = "success"
                    console.print(f"    [green]Saved: {output_file}[/green]")
                else:
                    console.print(
                        f"    [red]No image in response for {input_file.name}[/red]"
                    )
            else:
                console.print(
                    f"    [red]Error: {response.status_code} - {response.text}[/red]"
                )

        except Exception as e:
            console.print(f"    [red]Error processing {input_file.name}: {e}[/red]")

        finally:
            duration = time.time() - start_time
            # Log to CSV
            with open(csv_file, "a", newline="") as f:
                writer = csv.writer(f)
                writer.writerow(
                    [
                        str(input_file),
                        str(output_file),
                        api_room_type,
                        api_style,
                        f"{duration:.2f}",
                        status,
                    ]
                )

        progress.advance(task)

console.print("[bold green]Processing complete.[/bold green]")
