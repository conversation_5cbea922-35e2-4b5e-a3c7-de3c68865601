from yoyo import step

__depends__ = {}

steps = [
    step(
        """
    CREATE TABLE IF NOT EXISTS room_type (
        id SERIAL PRIMARY KEY,
        name VARCHA<PERSON>(255) NOT NULL
        
    );
    """,
        """
    DROP TABLE IF EXISTS room_type;
    """,
    ),
    step(
        """
    CREATE TABLE IF NOT EXISTS category (
        id SERIAL PRIMARY KEY,
        room_type_id INTEGER NOT NULL,
        name VARCHA<PERSON>(255) NOT NULL  
    );
    """,
        """
    DROP TABLE IF EXISTS category;
    """,
    ),
    step(
        """
    ALTER TABLE category
    ADD CONSTRAINT fk_category_room_type
    FOREIGN KEY (room_type_id) REFERENCES room_type(id);
    """,
        """
    ALTER TABLE category
    DROP CONSTRAINT fk_category_room_type;
    """,
    ),
    step(
        """
    create table if not exists item (
        id serial primary key,
        category_id integer not null,
        name varchar(255) not null,
        description text,
        image_url varchar(255),
        item_link varchar(255)
    );
    """,
        """
    drop table if exists item;
    """,
    ),
    step(
        """
    alter table item
    add constraint fk_item_category
    foreign key (category_id) references category(id);
    """,
        """
    alter table item
    drop constraint fk_item_category;
    """,
    ),
]
