from typing import List, Optional
from pydantic import BaseModel
from common.core.schema import RequestSchema, ResponseSchema
from enum import Enum
class ImageRequest(RequestSchema):
    room_image: str
    product_name: str
    product_image: str
    product_description: str


class ImageResponse(BaseModel):
    image: str
    
    
class Product(Enum):
    FLOORING = "flooring"
    KITCHEN = "kitchen"
    OTHER = "other"
class ProductType(BaseModel):
    product_type: Product