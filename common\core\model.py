from abc import ABC, abstractmethod
from typing import Any, <PERSON><PERSON>
from .schema import TokenUsage
class CoreModel(ABC):
    def __init__(self, *args, **kwargs):
        self.client = None

    @abstractmethod
    def set_client(self, *args, **kwargs):
        pass

    @abstractmethod
    async def image_edit(self, *args, **kwargs)-> <PERSON><PERSON>[Any, TokenUsage] | Exception:
        pass

    @abstractmethod
    async def chat_completion(self, *args, **kwargs)-> Tuple[Any, TokenUsage] | Exception:
        pass
